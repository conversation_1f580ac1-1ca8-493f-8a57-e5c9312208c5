import uuid
from email.policy import default

import imgkit
from PIL import Image
from bson import ObjectId
from mobio.libs.logging import MobioLogging


from src.common.handle_queue import HandleQueue
from src.common.utils import generate_session, generate_name_unique
from src.common import EmailTemplate, LANG, CommonParams, EMK_CONFIG_HTML_TO_IMAGE_BIN_PATH, ObjectsTemplate, \
    SendTestEmail
from src.common.mobio_exception import CustomError
from src.controllers import MobioTemplateBaseController
from src.internal_module.notify_management.notify_management import CallToNotifyManagement
from src.models.mongodb.email_builder_model import EmailBuilderModel
from src.models.mongodb.email_template_favorites_model import EmailTemplateFavoritesModel
from src.models.mongodb.log_action import LogActionModel
from mobio.sdks.admin import MobioAdminSDK
from flask import request
import datetime
from src.models.mongodb.email_template_sample_library_model import EmailTemplateSampleLibraryModel
from mobio.libs.validator import In, InstanceOf, Length, Required
from src.common.utils import utf8_to_ascii
from src.models.mongodb.email_template_model import EmailTemplateModel

class EmailTemplateController(MobioTemplateBaseController):

    def __init__(self):
        super().__init__()
        self.class_name = self.__class__.__name__

    def validate_field_email_template(self, data_input):
        # Set default value for description if not provided
        if EmailTemplate.DESCRIPTION not in data_input or data_input[EmailTemplate.DESCRIPTION] is None:
            data_input[EmailTemplate.DESCRIPTION] = ""

        rule_input = {
            EmailTemplate.STATUS: [Required, InstanceOf(int), In([CommonParams.PUBLIC, CommonParams.PRIVATE])],
            EmailTemplate.NAME: [Required, Length(1, 256)],
            EmailTemplate.BODY: [Required, InstanceOf(dict)],
            EmailTemplate.CATEGORIES: [InstanceOf(list)],
            EmailTemplate.DESCRIPTION: [Length(0, 512)],
            EmailTemplate.THUMBNAIL: [InstanceOf(str)],
            EmailTemplate.SMALL_THUMBNAIL: [InstanceOf(str)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_field_update_email_template(self, data_input):
        rule_input = {
            EmailTemplate.STATUS: [InstanceOf(int), In([CommonParams.PUBLIC, CommonParams.PRIVATE])],
            EmailTemplate.NAME: [Length(0, 256)],
            EmailTemplate.BODY: [InstanceOf(dict)],
            EmailTemplate.CATEGORIES: [InstanceOf(list)],
            EmailTemplate.DESCRIPTION: [Length(0, 512)],
            EmailTemplate.THUMBNAIL: [InstanceOf(str)],
            EmailTemplate.SMALL_THUMBNAIL: [InstanceOf(str)],
            CommonParams.SESSION: [Required, InstanceOf(str)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_delete_email_template(self, data_input):
        rule_input = {
            CommonParams.IDS: [Required, InstanceOf(list), Length(1)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_count_email_template_by_categories(self, data_input):
        rule_input = {
            CommonParams.CATEGORY_IDS: [Required, InstanceOf(list), Length(1)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    def validate_send_test_email(self, data_input):
        rule_input = {
            SendTestEmail.FROM: [Required, InstanceOf(str), Length(1, 256)],
            SendTestEmail.FROM_NAME: [Required, InstanceOf(str), Length(1, 256)],
            SendTestEmail.TITLE: [Required, InstanceOf(str), Length(1, 320)],
            SendTestEmail.SEND_TO: [Required, InstanceOf(list)],
            SendTestEmail.BODY: [Required, InstanceOf(str)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    @staticmethod
    def gen_html_to_img(html, file_name):
        config = imgkit.config(wkhtmltoimage=EMK_CONFIG_HTML_TO_IMAGE_BIN_PATH)

        options = {
            'encoding': 'UTF-8',
            'custom-header': [
                ('Accept-Encoding', 'gzip')
            ],
            'format': 'jpg',
            'quality': '80',
            'enable-local-file-access': None
        }

        imgkit.from_string(html, file_name, config=config, options=options)

    def _validate_update_email_template_favorite(self, data_input):
        rule_input = {
            "email_id": [Required, InstanceOf(str), Length(1)],
            "is_favorite": [Required, InstanceOf(bool), In([True, False])],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def _check_email_builder_custom_name_exist(self, name, email_id=None):
        email_builder = EmailBuilderModel().get_email_builder_by_name(name)
        if email_builder:
            if email_id and str(email_id) == str(email_builder["_id"]):
                return
            else:
                raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NAME_EXIST).get('message'))

    def _convert_detail_email_template(self, email, type=None):
        if "_id" in email:
            email["id"] = str(email.pop("_id"))
        if "name_unsigned" in email:
            email.pop("name_unsigned")
        if "description_unsigned" in email:
            email.pop("description_unsigned")
        if type and type == "get_list":
            email.pop(EmailTemplate.BODY)
        created_time = email["created_time"]
        if isinstance(created_time, datetime.datetime):
            created_time = created_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        email["created_time"] = created_time
        updated_time = email["updated_time"]
        if isinstance(updated_time, datetime.datetime):
            updated_time = updated_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        email["updated_time"] = updated_time
        return email

    def update_email_template_favorite(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        self._validate_update_email_template_favorite(request.json)
        email_id = request.json.get("email_id")
        is_favorite = request.json.get("is_favorite")
        EmailTemplateFavoritesModel().update_email_template_favorite(account_id, merchant_id, email_id, is_favorite)
        return

    def thread_generate_thumbs(self, file_path, content):
        func_name = self.thread_generate_thumbs.__name__
        try:
            EmailTemplateController.gen_html_to_img(content, file_path)
            image = Image.open(file_path)
            image.save(file_path, quality=20, optimize=True)
        except Exception as ex:
            MobioLogging().info('{} :: {} Exception: {}'.format(self.class_name, func_name, ex))

    def add_email_template(self):
        func_name = self.add_email_template.__name__
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')

        body = request.get_json()
        self.validate_field_email_template(body)

        data_insert = body
        name_email = body.get(EmailTemplate.NAME).strip()
        self._check_email_builder_custom_name_exist(name_email)
        if not data_insert.get(EmailTemplate.CATEGORIES):
            data_insert[EmailTemplate.CATEGORIES] = []

        name_unsigned = generate_name_unique(name_email)
        description_unsigned = generate_name_unique(data_insert.get(EmailTemplate.DESCRIPTION))
        data_insert.update({
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.CREATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            CommonParams.CREATED_BY: account_id,
            CommonParams.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            CommonParams.UPDATED_BY: account_id,
            CommonParams.SESSION: generate_session(),
            EmailTemplate.NAME: name_email,
            EmailTemplate.NAME_UNSIGNED: name_unsigned,
            EmailTemplate.DESCRIPTION_UNSIGNED: description_unsigned
        })

        email_builder_id = str(EmailBuilderModel().insert_document(data_insert).inserted_id)
        if not email_builder_id:
            raise CustomError(self.lang.get(LANG.ADD_EMAIL_TEMPLATE_ERROR).get('message'))
        MobioLogging().info('{} :: {} email_template_id: '.format(self.class_name, func_name, email_builder_id))
        data_insert.update({
            CommonParams.ID: email_builder_id
        })
        return {
            'data': self._convert_detail_email_template(data_insert)
        }

    def count_email_template(self):
        # Lấy danh sách email_id favorite
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        is_favorite = request.args.get(CommonParams.IS_FAVORITE, False)
        search = request.args.get(CommonParams.SEARCH)
        category_ids = request.args.get(CommonParams.CATEGORY_IDS)

        ids_email_favorites = []
        if is_favorite:
            # Lấy danh sách email_id favorite
            email_favorites = EmailTemplateFavoritesModel().get_email_template_favorites_by_account_id(account_id,
                                                                                                       merchant_id)
            ids_email_favorites = [ObjectId(email["email_id"]) for email in email_favorites]

        search_options_public = {
            CommonParams.MERCHANT_ID: merchant_id,
            EmailTemplate.STATUS: CommonParams.PUBLIC
        }
        search_options_private = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.CREATED_BY: account_id,
            EmailTemplate.STATUS: CommonParams.PRIVATE
        }
        search_option_email_custom = {
            CommonParams.MERCHANT_ID: merchant_id
        }

        if ids_email_favorites:
            search_options_public.update({CommonParams.ID: { "$in": ids_email_favorites }})
            search_options_private.update({CommonParams.ID: { "$in": ids_email_favorites }})
            search_option_email_custom.update({CommonParams.ID: {"$in": ids_email_favorites}})
        if search:
            search = utf8_to_ascii(search.lower())
            search_options_public.update({EmailTemplate.NAME_UNSIGNED: {"$regex": search }})
            search_options_private.update({EmailTemplate.NAME_UNSIGNED: {"$regex": search }})
            search_option_email_custom.update({EmailTemplate.NAME_UNSIGNED: {"$regex": search}})
        if category_ids:
            lst_category_id = category_ids.split(",")
            search_options_public.update({EmailTemplate.CATEGORIES: {
                "$in": lst_category_id }
            })
            search_options_private.update({EmailTemplate.CATEGORIES: {
                "$in": lst_category_id }
            })
            search_option_email_custom.update({EmailTemplate.CATEGORIES: {
                "$in": lst_category_id }
            })
        email_template_count_public = EmailBuilderModel().count_all_email_builder(search_options_public)

        email_template_count_private = EmailBuilderModel().count_all_email_builder(search_options_private)

        email_template_count_email_custom = EmailBuilderModel().count_all_email_builder(search_option_email_custom)
        result = [
            {"type": "email_custom", "count": email_template_count_email_custom},
            {"type": "public", "count": email_template_count_public},
            {"type": "private", "count": email_template_count_private},
        ]
        return {
            "data": result
        }

    def get_list_email_template(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        args = request.args
        is_favorite = args.get(CommonParams.IS_FAVORITE, False)
        status = args.get(EmailTemplate.STATUS)
        search = args.get(CommonParams.SEARCH)
        categories = args.get(EmailTemplate.CATEGORIES)

        sort_by = args.get("sort_by", "updated_type")
        order_by = args.get("order_by", -1, type=int)

        per_page = args.get(CommonParams.PER_PAGE, default=20, type=int)
        after_token = args.get("after_token", default=None)

        emails_builder, new_after_token = EmailBuilderModel().get_emails_builder_by_condition(
            merchant_id,
            account_id,
            is_favorite,
            status,
            search,
            categories,
            sort_by,
            order_by,
            per_page,
            after_token
        )

        data_return = []
        for email in emails_builder:
            if is_favorite:
                email["is_favorite"] = True
            else:
                email_id = str(email["_id"])
                email_favorite = EmailTemplateFavoritesModel().get_email_template_favorite_by_email_id(account_id,merchant_id,email_id)
                email["is_favorite"] = True if email_favorite else False
            result = self._convert_detail_email_template(email, type="get_list")
            data_return.append(result)

        return {
            "data": data_return,
            "paging": {"cursors": {"after_token": new_after_token, "before_token": after_token}}
        }

    def update_email_template(self, email_template_id):
        func_name = self.update_email_template.__name__
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        self.validate_format_object_id(email_template_id)

        body = request.get_json()
        self.validate_field_update_email_template(body)

        detail_email_template = EmailBuilderModel().get_detail_email_builder(email_template_id, merchant_id)
        if not detail_email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))
        # Check session
        if detail_email_template.get(CommonParams.SESSION, "") != body.get(CommonParams.SESSION, ""):
            raise CustomError(self.lang.get(LANG.SESSION_DISSIMILARITY).get("message"))
        session = generate_session()
        list_link_thumbnail_old, list_link_small_thumbnail_old = [], []

        data_update = {
            CommonParams.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            CommonParams.UPDATED_BY: account_id,
            CommonParams.SESSION: session,
        }

        # Chỉ cập nhật các trường có trong body request
        if EmailTemplate.NAME in body and body.get(EmailTemplate.NAME).strip():
            name = body.get(EmailTemplate.NAME).strip()
            name_unsigned = generate_name_unique(name)
            self._check_email_builder_custom_name_exist(name)
            data_update[EmailTemplate.NAME] = name
            data_update[EmailTemplate.NAME_UNSIGNED] = name_unsigned
        if EmailTemplate.DESCRIPTION in body:
            data_update[EmailTemplate.DESCRIPTION] = body.get(EmailTemplate.DESCRIPTION)
            data_update[EmailTemplate.DESCRIPTION_UNSIGNED] = generate_name_unique(body.get(EmailTemplate.DESCRIPTION))
        if EmailTemplate.BODY in body:
            data_update[EmailTemplate.BODY] = body.get(EmailTemplate.BODY)
        if EmailTemplate.STATUS in body:
            data_update[EmailTemplate.STATUS] = body.get(EmailTemplate.STATUS)
        if EmailTemplate.CATEGORIES in body:
            data_update[EmailTemplate.CATEGORIES] = body.get(EmailTemplate.CATEGORIES)
        if EmailTemplate.THUMBNAIL in body and body.get(EmailTemplate.THUMBNAIL).strip():
            if body.get(EmailTemplate.THUMBNAIL) != detail_email_template.get(EmailTemplate.THUMBNAIL):
                list_link_thumbnail_old.append(detail_email_template.get(EmailTemplate.THUMBNAIL, ""))
            data_update[EmailTemplate.THUMBNAIL] = body.get(EmailTemplate.THUMBNAIL)
        if EmailTemplate.SMALL_THUMBNAIL in body and body.get(EmailTemplate.SMALL_THUMBNAIL).strip():
            if body.get(EmailTemplate.SMALL_THUMBNAIL) != detail_email_template.get(EmailTemplate.SMALL_THUMBNAIL):
                list_link_small_thumbnail_old.append(detail_email_template.get(EmailTemplate.SMALL_THUMBNAIL, ""))
            data_update[EmailTemplate.SMALL_THUMBNAIL] = body.get(EmailTemplate.SMALL_THUMBNAIL)

        search_option = {
            CommonParams.ID: ObjectId(email_template_id)
        }
        result_update = EmailBuilderModel().update_one_query(query=search_option, data=data_update)
        LogActionModel().insert_log_action(
            merchant_id, account_id, "update", 1, "email_template", email_template_id
        )
        MobioLogging().info('{} :: {} status_delete: {}'.format(self.class_name, func_name, result_update))
        if result_update:
            self._handle_thumbnail_deletion_queue(merchant_id, list_link_thumbnail_old, list_link_small_thumbnail_old)
            email_update = EmailBuilderModel().get_detail_email_builder(email_template_id, merchant_id)
            return self._convert_detail_email_template(email_update, type="get_list")
        raise CustomError

    def _handle_thumbnail_deletion_queue(self, merchant_id, list_link_thumbnail_old, list_link_small_thumbnail_old):
        list_link_thumbnail_delete = list_link_thumbnail_old + list_link_small_thumbnail_old
        if list_link_thumbnail_delete:
            message_queue = {"merchant_id": merchant_id, "list_thumbnail": list_link_thumbnail_delete}
            HandleQueue.push_message_delete_info_publish_thumbnail_queue(message_queue)

    def delete_email_template(self):
        func_name = self.delete_email_template.__name__
        merchant_id = EmailTemplateController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        ids = request.args.get(CommonParams.IDS)
        ids = [email_template_id for email_template_id in ids.split(',') if email_template_id] if ids else None
        validate_data = {
            CommonParams.IDS: ids
        }
        self.validate_delete_email_template(validate_data)
        ids = [ObjectId(id) for id in ids if ObjectId.is_valid(id)]

        MobioLogging().info('{} :: {} ids: '.format(self.class_name, func_name, ids))
        emails_builder = EmailBuilderModel().get_list_email_builder_by_ids(merchant_id, ids)

        # Lọc ra các email_builder có thể xóa (public hoặc private của chính mình được phép xoá)
        emails_builder_delete = self.get_emails_builder_delete(emails_builder, account_id)
        list_link_thumbnail, list_link_small_thumbnail = [], []
        ids_email_builder_delete = []
        for email_builder in emails_builder_delete:
            ids_email_builder_delete.append(email_builder.get(CommonParams.ID))
            list_link_thumbnail.append(email_builder.get(EmailTemplate.THUMBNAIL))
            list_link_small_thumbnail.append(email_builder.get(EmailTemplate.SMALL_THUMBNAIL))

        status = EmailBuilderModel().delete_multi_email_builder(merchant_id, ids_email_builder_delete)
        LogActionModel().insert_log_action(
            merchant_id, account_id, "delete", 1, "email_template", ids
        )
        if status:
            self._handle_thumbnail_deletion_queue(merchant_id, list_link_thumbnail, list_link_small_thumbnail)
            return
        raise CustomError(self.lang.get(LANG.DELETE_EMAIL_TEMPLATE_ERROR).get('message'))

    def get_detail_email_template(self, email_template_id):
        account_id = MobioAdminSDK().get_value_from_token('id')
        merchant_id = EmailTemplateController.get_merchant_header()
        detail_email_template = EmailBuilderModel().get_detail_email_builder(email_template_id, merchant_id)
        if not detail_email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))

        data_return = self._convert_detail_email_template(detail_email_template)
        email_builder_favorite = EmailTemplateFavoritesModel().find_one({
            "account_id": account_id,
            "merchant_id": merchant_id,
            "email_id": email_template_id
        })
        data_return.update({EmailTemplate.IS_FAVORITE: True} if email_builder_favorite else {EmailTemplate.IS_FAVORITE: False})
        return {
            'data': data_return
        }

    def check_email_builder_name_exist(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        name = request.args.get(EmailTemplate.NAME).strip()
        if not name:
            raise CustomError(self.lang.get(LANG.NAME_CATEGORY_EMAIL_BUILDER_EMPTY).get('message'))
        email_builder = EmailBuilderModel().find_one({
            CommonParams.MERCHANT_ID: merchant_id,
            EmailTemplate.NAME: name
        })
        if not email_builder:
            return {"data":{"is_exists": True}}
        return {"data":{"is_exists": False}}

    def upload_image_email_builder(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        file = request.files.get('file')
        email_id = str(uuid.uuid4())
        if not file:
            raise CustomError(self.lang.get(LANG.FILE_NOT_FOUND).get('message'))
        avatar_info = self.upload_image_with_file_data(merchant_id, file, email_id)
        if not avatar_info:
            raise CustomError(self.lang.get(LANG.CREATE_IMG_ERROR).get('message'))

        return {
            'data': avatar_info
        }

    def send_test_email(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        body = request.get_json()
        self.validate_send_test_email(body)

        send_from = body.get(SendTestEmail.FROM)
        domain = send_from.split("@")[1]
        send_from_name = body.get(SendTestEmail.FROM_NAME)
        send_to = body.get(SendTestEmail.SEND_TO)
        title = body.get(SendTestEmail.TITLE)
        body_email = body.get(SendTestEmail.BODY)

        lst_email_send_to = []
        for email in send_to:
            lst_email_send_to.append({"email": email})

        payload_send = {
            "from": {
                "source": "mobio_template",
                "merchant_id": merchant_id,
                "sender_id": send_from,
                "sender_name": send_from_name,
                "sender_domain": domain,
                "sender_type": "BROAD_CAST",
                "anti_spam": False,
                "push_event": True,
            },
            "to": [
                {
                    "to": lst_email_send_to,
                    "body": {"subject": title, "content": body_email},
                }
            ],
        }
        result = CallToNotifyManagement().send_email_test(
            merchant_id, payload_send
        )
        if not result:
            raise CustomError()
        return

    def get_emails_builder_delete(self, emails_builder, account_id):
        emails_builder_delete = []
        for email_builder in emails_builder:
            if email_builder.get(EmailTemplate.STATUS) == CommonParams.PUBLIC:
                emails_builder_delete.append(email_builder)
            if email_builder.get(EmailTemplate.STATUS) == CommonParams.PRIVATE and email_builder.get(CommonParams.CREATED_BY) == account_id:
                emails_builder_delete.append(email_builder)
        return emails_builder_delete

    def count_email_template_by_category(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        category_ids = request.json.get(CommonParams.CATEGORY_IDS)
        category_ids = category_ids.split(",") if category_ids else []
        validate_data = {
            CommonParams.CATEGORY_IDS: category_ids
        }
        self.validate_count_email_template_by_categories(validate_data)
        data_return = []
        for category_id in category_ids:
            count = EmailBuilderModel().count_all_email_builder({
                CommonParams.MERCHANT_ID: merchant_id,
                EmailTemplate.CATEGORIES: {
                    "$in": [category_id]
                }
            })
            data_return.append({"category_id": category_id, "count": count})

        # Count email by category default (count những email có categories = [])
        count_email_by_category_default = EmailBuilderModel().count_all_email_builder({
            CommonParams.MERCHANT_ID: merchant_id,
            EmailTemplate.CATEGORIES: {'$size': 0 }
        })
        data_return.append({"category_id": "default","count": count_email_by_category_default})
        return {
            "data": data_return
        }

    def count_all_email_template(self):
        merchant_id = EmailTemplateController.get_merchant_header()
        count = EmailBuilderModel().count_all_email_builder({
            CommonParams.MERCHANT_ID: merchant_id
        })
        return {
            "data": {"count": count}
        }
