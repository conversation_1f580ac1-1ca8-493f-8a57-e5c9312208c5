import copy
import datetime
from mobio.libs.validator import In, InstanceOf, Length, Required
from src.common import LANG, EmailTemplate, EmailTemplateSampleLibrary
from src.common.mobio_exception import CustomError
from src.common.utils import utf8_to_ascii
from src.controllers import MobioTemplateBaseController
from src.models.mongodb.email_builder_model import EmailBuilderModel
from src.models.mongodb.email_template_branch_model import EmailTemplateBranchModel
from src.models.mongodb.email_template_favorites_model import EmailTemplateFavoritesModel
from src.models.mongodb.email_template_holiday_model import EmailTemplateHolidayModel
from src.models.mongodb.email_template_purpose_model import EmailTemplatePurposeModel
from mobio.sdks.admin import Mo<PERSON>AdminSDK
from flask import request
from src.models.mongodb.email_template_sample_library_model import EmailTemplateSampleLibraryModel


class EmailTemplateWareHouseController(MobioTemplateBaseController):

    def __init__(self):
        super().__init__()
        self.class_name = self.__class__.__name__
        
    def _convert_detail_email_template(self, email, type = None):
        if "_id" in email:
            email["id"] = str(email.pop("_id"))
        if "name_unsigned" in email:
            email.pop("name_unsigned")
        if "description_unsigned" in email:
            email.pop("description_unsigned")
        if type and type == "get_list":
            email.pop(EmailTemplate.BODY)
        created_time = email["created_time"]
        if isinstance(created_time, datetime.datetime):
            created_time = created_time.strftime("%Y-%m-%dT%H:%MZ")
        email["created_time"] = created_time
        updated_time = email["updated_time"]
        if isinstance(updated_time, datetime.datetime):
            updated_time = updated_time.strftime("%Y-%m-%dT%H:%MZ")
        email["updated_time"] = updated_time
        return email
    
    def _validate_sample_library_email_template(self, data_input):
        rule_input = {
            "name": [Required, InstanceOf(str), Length(0,256)],
            "body": [Required, InstanceOf(dict), Length(1)],
            "thumbnail": [InstanceOf(str), Length(1)],
            "small_thumbnail": [InstanceOf(str), Length(1)],
            "description": [InstanceOf(str), Length(0,512)],
            "holiday_codes": [InstanceOf(str), Length(1)],
            "purpose_codes": [ InstanceOf(str), Length(1)],
            "branch_codes": [ InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_input, data_input)
        
    def _check_email_template_name_exist(self, name, email_id=None):
        email_template = EmailTemplateSampleLibraryModel().get_email_template_by_name(name)
        if email_template:
            if email_id and str(email_id) == str(email_template["_id"]):
                return
            else:
                raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NAME_EXIST).get('message'))
        
    def get_list_email_template_branch(self):
        list_branch = EmailTemplateBranchModel().get_list_branch()
        result = []
        for branch in list_branch:
            result.append({
                "branch_code": branch["branch_code"],
                "branch_name": branch.get("branch_name", {}).get(self.language)
            })
        return result
    
    def get_list_email_template_purpose(self):
        list_purpose = EmailTemplatePurposeModel().get_list_purpose()   
        result = []
        for purpose in list_purpose:
            result.append({
                "purpose_code": purpose["purpose_code"],
                "purpose_name": purpose.get("purpose_name", {}).get(self.language)
            })
        return result
    
    def get_list_email_template_holiday(self):
        list_holiday = EmailTemplateHolidayModel().get_list_holiday()    
        result = []
        for holiday in list_holiday:
            result.append({
                "holiday_code": holiday["holiday_code"],
                "holiday_name": holiday.get("holiday_name", {}).get(self.language)
            })
        return result

    def get_list_email_template_sample_library(self):
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        args = request.args
        search = args.get("search")
        is_favorite = args.get("is_favorite")
        branch_code = args.get("branch_code")
        purpose_code = args.get("purpose_code")
        holiday_code = args.get("holiday_code")
        after_token = args.get("after_token", default=None)
        per_page = int(args.get("per_page", default=20))
        
        sort_by = args.get("sort_by", "updated_time")
        order_by = args.get("order_by", -1, type=int)
        
        email_templates, new_after_token = EmailTemplateSampleLibraryModel().get_list_email_template_sample_library(
            merchant_id, 
            account_id, 
            search, 
            is_favorite, 
            branch_code, 
            purpose_code, 
            holiday_code, 
            sort_by, 
            order_by,
            after_token,
            per_page
        )
        results = []
        for email in email_templates:
            if is_favorite:
                email["is_favorite"] = True
            else:
                email_id = str(email["_id"])
                email_favarite = EmailTemplateFavoritesModel().get_email_template_favorite_by_email_id(account_id, merchant_id, email_id)
                email["is_favorite"] = True if email_favarite else False
            result = self._convert_detail_email_template(email, type="get_list")
            results.append(result)
        return {
            "data": results,
            "paging": {"cursors": {"after_token": new_after_token, "before_token": after_token}},
        }
        
    def get_detail_email_template_sample_library(self, email_id):  
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id') 
        email_template = EmailTemplateSampleLibraryModel().get_email_template_by_id(email_id)
        if not email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))
        email_favorite = EmailTemplateFavoritesModel().get_email_template_favorite_by_email_id(merchant_id, account_id, email_id)
        if email_favorite:
            email_template[EmailTemplateSampleLibrary.IS_FAVORITE] = True   
        else:
            email_template[EmailTemplateSampleLibrary.IS_FAVORITE] = False            
        result = self._convert_detail_email_template(email_template)
        return {
            "data": result
        }   
        
    def copy_email_template_to_sample_library(self):        
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        email_id = request.json.get("email_id")
        email_template = EmailBuilderModel().get_detail_email_builder(email_id, merchant_id)
        if not email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))
        email_template.update({
            EmailTemplateSampleLibrary.ID: email_template[EmailTemplate.ID],
            EmailTemplateSampleLibrary.NAME: email_template[EmailTemplate.NAME],
            EmailTemplateSampleLibrary.NAME_UNSIGNED: email_template[EmailTemplate.NAME_UNSIGNED],
            EmailTemplateSampleLibrary.DESCRIPTION: email_template[EmailTemplate.DESCRIPTION],
            EmailTemplateSampleLibrary.DESCRIPTION_UNSIGNED: email_template[EmailTemplate.DESCRIPTION_UNSIGNED],
            EmailTemplateSampleLibrary.BODY: email_template[EmailTemplate.BODY],
            EmailTemplateSampleLibrary.THUMBNAIL: email_template[EmailTemplate.THUMBNAIL],
            EmailTemplateSampleLibrary.SMALL_THUMBNAIL: email_template[EmailTemplate.SMALL_THUMBNAIL],
            EmailTemplateSampleLibrary.HOLIDAY_CODES : [],
            EmailTemplateSampleLibrary.PURPOSE_CODES: [],
            EmailTemplateSampleLibrary.BRANCH_CODES: [],        
            EmailTemplateSampleLibrary.CREATED_BY: account_id,
            EmailTemplateSampleLibrary.UPDATED_BY: account_id,
            EmailTemplateSampleLibrary.CREATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            EmailTemplateSampleLibrary.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc)
        })
        self._check_email_template_name_exist(email_template[EmailTemplateSampleLibrary.NAME])
        email_template_id = EmailTemplateSampleLibraryModel().insert_document(email_template).inserted_id
        if not email_template_id:
            raise CustomError(self.lang.get(LANG.ADD_EMAIL_TEMPLATE_ERROR).get('message'))
        return {
            'data': self.json_encoder(email_template)
        }
        
    def create_email_template_sample_library(self):
        account_id = MobioAdminSDK().get_value_from_token('id')
        body = request.get_json()
        self._validate_sample_library_email_template(body)
        data_insert = copy.deepcopy(body)
        name = body.get(EmailTemplateSampleLibrary.NAME).strip()
        name_unsigned = utf8_to_ascii(name.lower())
        self._check_email_template_name_exist(name)
        description = body.get(EmailTemplateSampleLibrary.DESCRIPTION).strip() if body.get(EmailTemplateSampleLibrary.DESCRIPTION) else None
        description_unsigned = utf8_to_ascii(description.lower()) if body.get(EmailTemplateSampleLibrary.DESCRIPTION) else None
        holiday_codes = body.get(EmailTemplateSampleLibrary.HOLIDAY_CODES, []).split(",") if body.get(EmailTemplateSampleLibrary.HOLIDAY_CODES) else []
        purpose_codes = body.get(EmailTemplateSampleLibrary.PURPOSE_CODES, []).split(",") if body.get(EmailTemplateSampleLibrary.PURPOSE_CODES) else []
        branch_codes = body.get(EmailTemplateSampleLibrary.BRANCH_CODES, []).split(",") if body.get(EmailTemplateSampleLibrary.BRANCH_CODES) else []
        email_body = body.get(EmailTemplateSampleLibrary.BODY)
        thumbnail = body.get(EmailTemplateSampleLibrary.THUMBNAIL)
        small_thumbnail = body.get(EmailTemplateSampleLibrary.SMALL_THUMBNAIL)
        
        data_insert.update({
            EmailTemplateSampleLibrary.NAME: name,
            EmailTemplateSampleLibrary.DESCRIPTION: description,
            EmailTemplateSampleLibrary.NAME_UNSIGNED: name_unsigned,
            EmailTemplateSampleLibrary.DESCRIPTION_UNSIGNED: description_unsigned,
            EmailTemplateSampleLibrary.BODY: email_body,
            EmailTemplateSampleLibrary.THUMBNAIL: thumbnail,
            EmailTemplateSampleLibrary.SMALL_THUMBNAIL: small_thumbnail,
            EmailTemplateSampleLibrary.CREATED_BY: account_id,
            EmailTemplateSampleLibrary.UPDATED_BY: account_id,
            EmailTemplateSampleLibrary.HOLIDAY_CODES: holiday_codes,
            EmailTemplateSampleLibrary.PURPOSE_CODES: purpose_codes,
            EmailTemplateSampleLibrary.BRANCH_CODES: branch_codes,  
            EmailTemplateSampleLibrary.CREATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            EmailTemplateSampleLibrary.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc),
        })
        email_template_id = EmailTemplateSampleLibraryModel().insert_document(data_insert).inserted_id
        if not email_template_id:
            raise CustomError(self.lang.get(LANG.ADD_EMAIL_TEMPLATE_ERROR).get('message'))
        return {
            'data': self._convert_detail_email_template(data_insert)
        }
        

    def update_email_template_sample_library(self, email_id):   
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        body = request.get_json()
        self._validate_sample_library_email_template(body)
        email_template = EmailTemplateSampleLibraryModel().get_email_template_by_id(email_id)
        if not email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))
        data_update = copy.deepcopy(body)
        name = body.get(EmailTemplateSampleLibrary.NAME).strip()
        name_unsigned = utf8_to_ascii(name.lower())
        self._check_email_template_name_exist(name, email_id)
        description = body.get(EmailTemplateSampleLibrary.DESCRIPTION).strip()
        description_unsigned = utf8_to_ascii(description.lower())
        holiday_codes = body.get(EmailTemplateSampleLibrary.HOLIDAY_CODES, []).split(",")
        purpose_codes = body.get(EmailTemplateSampleLibrary.PURPOSE_CODES, []).split(",")
        branch_codes = body.get(EmailTemplateSampleLibrary.BRANCH_CODES, []).split(",")
        email_body = body.get(EmailTemplateSampleLibrary.BODY)
        thumbnail = body.get(EmailTemplateSampleLibrary.THUMBNAIL)
        small_thumbnail = body.get(EmailTemplateSampleLibrary.SMALL_THUMBNAIL)
        
        data_update.update({
            EmailTemplateSampleLibrary.NAME: name,
            EmailTemplateSampleLibrary.DESCRIPTION: description,
            EmailTemplateSampleLibrary.NAME_UNSIGNED: name_unsigned,
            EmailTemplateSampleLibrary.DESCRIPTION_UNSIGNED: description_unsigned,
            EmailTemplateSampleLibrary.BODY: email_body,
            EmailTemplateSampleLibrary.THUMBNAIL: thumbnail,
            EmailTemplateSampleLibrary.SMALL_THUMBNAIL: small_thumbnail,
            EmailTemplateSampleLibrary.UPDATED_BY: account_id,
            EmailTemplateSampleLibrary.HOLIDAY_CODES: holiday_codes,
            EmailTemplateSampleLibrary.PURPOSE_CODES: purpose_codes,
            EmailTemplateSampleLibrary.BRANCH_CODES: branch_codes,  
            EmailTemplateSampleLibrary.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc),
        })
        EmailTemplateSampleLibraryModel().update_email_template_sample_library(email_id, data_update)
        return 
        
    def delete_email_template_sample_library(self, email_id):
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        email_template = EmailTemplateSampleLibraryModel().get_email_template_by_id(email_id)
        if not email_template:
            raise CustomError(self.lang.get(LANG.EMAIL_TEMPLATE_NOT_EXIST).get('message'))
        EmailTemplateSampleLibraryModel().delete_email_template_sample_library(email_id)
        return 
    
    def count_email_template_sample_library(self):  
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        merchant_id = EmailTemplateWareHouseController.get_merchant_header()
        account_id = MobioAdminSDK().get_value_from_token('id')
        args = request.args
        search = args.get("search")
        is_favorite = args.get("is_favorite")
        branch_code = args.get("branch_code")
        purpose_code = args.get("purpose_code")
        holiday_code = args.get("holiday_code")
        
        total_email_template = EmailTemplateSampleLibraryModel().count_email_template_sample_library(
            merchant_id, 
            account_id, 
            search, 
            is_favorite, 
            branch_code, 
            purpose_code, 
            holiday_code
        )
        return {
            "data": {"total_sample_email_template": total_email_template}
        }
