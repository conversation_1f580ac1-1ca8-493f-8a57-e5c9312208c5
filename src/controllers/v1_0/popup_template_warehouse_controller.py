#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
import datetime
from mobio.libs.validator import In, InstanceOf, Length, Required
from src.common.mobio_exception import CustomError
from src.common.utils import utf8_to_ascii
from src.controllers import MobioTemplateBaseController
from mobio.sdks.admin import MobioAdminSDK
from flask import request


class PopupTemplateWareHouseController(MobioTemplateBaseController):

    def __init__(self):
        super().__init__()
        self.class_name = self.__class__.__name__

    # def _convert_detail_popup_template(self, email, type=None):
    #     if "_id" in email:
    #         email["id"] = str(email.pop("_id"))
    #     if "name_unsigned" in email:
    #         email.pop("name_unsigned")
    #     if "description_unsigned" in email:
    #         email.pop("description_unsigned")
    #     if type and type == "get_list":
    #         email.pop(EmailTemplate.BODY)
    #     created_time = email["created_time"]
    #     if isinstance(created_time, datetime.datetime):
    #         created_time = created_time.strftime("%Y-%m-%dT%H:%MZ")
    #     email["created_time"] = created_time
    #     updated_time = email["updated_time"]
    #     if isinstance(updated_time, datetime.datetime):
    #         updated_time = updated_time.strftime("%Y-%m-%dT%H:%MZ")
    #     email["updated_time"] = updated_time
    #     return email

    def validate_field_create_popup_template_warehouse(self, data_input):
        rule_input = {
            "name": [Required, InstanceOf(str), Length(0, 256)],
            "body": [Required, InstanceOf(dict), Length(1)],
            "thumbnail": [InstanceOf(str), Length(1)],
            "small_thumbnail": [InstanceOf(str), Length(1)],
            "description": [InstanceOf(str), Length(0, 512)],
            "holiday_codes": [InstanceOf(str), Length(1)],
            "purpose_codes": [InstanceOf(str), Length(1)],
            "branch_codes": [InstanceOf(str), Length(1)],
        }
        self.abort_if_validate_error(rule_input, data_input)

    def create_popup_template_warehouse(self):
        account_id = MobioAdminSDK().get_value_from_token('id')
        body = request.get_json()
        self.validate_field_create_popup_template_warehouse(body)
        print("==============CHECK VAO DEN DAY RUI NHE =================\n")
        print("==============CHECK VAO DEN DAY RUI NHE =================\n")