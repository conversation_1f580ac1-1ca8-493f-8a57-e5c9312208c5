#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
import datetime

from bson import ObjectId
from mobio.libs.validator import In, InstanceOf, Length, Required

from src.common import CommonParams, PopupTemplateWarehouse, LANG
from src.common.mobio_exception import CustomError
from src.common.utils import utf8_to_ascii
from src.controllers import MobioTemplateBaseController
from mobio.sdks.admin import MobioAdminSDK
from flask import request

from src.models.mongodb.popup_template_warehouse_branch_model import PopupTemplateWareHouseBranchModel
from src.models.mongodb.popup_template_warehouse_holiday_model import PopupTemplateWareHouseHolidayModel
from src.models.mongodb.popup_template_warehouse_purpose_model import PopupTemplateWareHousePurposeModel
from src.models.mongodb.popup_template_warehouse_type_model import PopupTemplateWareHouseTypesModel
from src.models.mongodb.popup_template_warehouse_model import PopupTemplateWareHouseModel
from src.models.popup_template_favoirites_model import PopupTemplateFavoritesModel


class PopupTemplateWareHouseController(MobioTemplateBaseController):

    def __init__(self):
        super().__init__()
        self.class_name = self.__class__.__name__

    def _convert_detail_popup_template(self, popup_template, type=None):
        if CommonParams.ID in popup_template:
            popup_template["id"] = str(popup_template.pop(CommonParams.ID))
        if PopupTemplateWarehouse.TITLE_UNIQUE in popup_template:
            popup_template.pop(PopupTemplateWarehouse.TITLE_UNIQUE)
        if type and type == "get_list":
            popup_template.pop(PopupTemplateWarehouse.CONTENT)
        created_time = popup_template[CommonParams.CREATED_TIME]
        if isinstance(created_time, datetime.datetime):
            created_time = created_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        popup_template[CommonParams.CREATED_TIME] = created_time
        updated_time = popup_template[CommonParams.UPDATED_TIME]
        if isinstance(updated_time, datetime.datetime):
            updated_time = updated_time.strftime("%Y-%m-%dT%H:%M:%SZ")
        popup_template[CommonParams.UPDATED_TIME] = updated_time
        return popup_template

    def get_list_branch(self):
        list_branch = PopupTemplateWareHouseBranchModel().get_list_branch()
        result = []
        for branch in list_branch:
            result.append({
                "branch_code": branch["branch_code"],
                "branch_name": branch.get("branch_name", {}).get(self.language)
            })
        return result

    def get_list_purpose(self):
        list_purpose = PopupTemplateWareHousePurposeModel().get_list_purpose()
        result = []
        for purpose in list_purpose:
            result.append({
                "purpose_code": purpose["purpose_code"],
                "purpose_name": purpose.get("purpose_name", {}).get(self.language)
            })
        return result

    def get_list_holiday(self):
        list_holiday = PopupTemplateWareHouseHolidayModel().get_list_holiday()
        result = []
        for holiday in list_holiday:
            result.append({
                "holiday_code": holiday["holiday_code"],
                "holiday_name": holiday.get("holiday_name", {}).get(self.language)
            })
        return result

    def get_list_types(self):
        list_types = PopupTemplateWareHouseTypesModel().get_list_types()
        result = []
        for type in list_types:
            result.append({
                "type_code": type["type_code"],
                "type_name": type.get("type_name", {}).get(self.language)
            })
        return result

    def validate_field_create_popup_template_warehouse(self, data_input):
        rule_input = {
            PopupTemplateWarehouse.TITLE: [Required, InstanceOf(str), Length(0, 256)],
            PopupTemplateWarehouse.CONTENT: [Required, InstanceOf(dict), Length(1)],
            PopupTemplateWarehouse.APPLIER_TYPE: [Required, InstanceOf(int), In([CommonParams.LIBRARY_TEMPLATE, CommonParams.LAYOUT_TEMPLATE])],
            PopupTemplateWarehouse.SECOND_PAGE: [InstanceOf(int), In([0, 1])],
            PopupTemplateWarehouse.THUMBNAIL: [InstanceOf(str), Length(1)],
            PopupTemplateWarehouse.SMALL_THUMBNAIL: [InstanceOf(str), Length(1)],
            PopupTemplateWarehouse.HOLIDAY_CODES: [Required, InstanceOf(list), Length(1)],
            PopupTemplateWarehouse.PURPOSE_CODES: [Required, InstanceOf(list), Length(1)],
            PopupTemplateWarehouse.BRANCH_CODES: [Required, InstanceOf(list), Length(1)],
            PopupTemplateWarehouse.TYPE_CODES: [Required,InstanceOf(list), Length(1)]
        }
        self.abort_if_validate_error(rule_input, data_input)

    def _check_title_is_exists(self, title):
        is_exists = PopupTemplateWareHouseModel().get_popup_template_warehouse_by_title(title)
        if is_exists:
            raise CustomError(self.lang.get(LANG.TITLE_POPUP_TEMPLATE_WAREHOUSE_EXISTS).get('message'))

    def create_popup_template_warehouse(self):
        account_id = MobioAdminSDK().get_value_from_token('id')
        body = request.get_json()
        self.validate_field_create_popup_template_warehouse(body)

        data_insert = body
        title = body.get(PopupTemplateWarehouse.TITLE).strip()
        self._check_title_is_exists(title)
        title_unique = utf8_to_ascii(title.lower())

        data_insert.update({
            PopupTemplateWarehouse.TITLE_UNIQUE: title_unique,
            PopupTemplateWarehouse.TITLE: title,
            CommonParams.CREATED_BY: account_id,
            CommonParams.UPDATED_BY: account_id,
            CommonParams.CREATED_TIME: datetime.datetime.now(datetime.timezone.utc),
            CommonParams.UPDATED_TIME: datetime.datetime.now(datetime.timezone.utc),
        })

        popup_template_warehouse_id = str(PopupTemplateWareHouseModel().insert_document(data_insert).inserted_id)
        if not popup_template_warehouse_id:
            raise CustomError(self.lang.get(LANG.ADD_POPUP_TEMPLATE_WAREHOUSE_ERROR).get('message'))
        data_insert.update({
            CommonParams.ID: popup_template_warehouse_id
        })
        return {
            'data': self._convert_detail_popup_template(data_insert)
        }


    def get_list_popup_template_warehouse(self):
        merchant_id = MobioAdminSDK().get_value_from_token('merchant_id')
        account_id = MobioAdminSDK().get_value_from_token('id')
        search = request.args.get(CommonParams.SEARCH)
        is_favorite = request.args.get(CommonParams.IS_FAVORITE)
        branch_codes = request.args.get(PopupTemplateWarehouse.BRANCH_CODES)
        purpose_codes = request.args.get(PopupTemplateWarehouse.PURPOSE_CODES)
        holiday_codes = request.args.get(PopupTemplateWarehouse.HOLIDAY_CODES)
        type_codes = request.args.get(PopupTemplateWarehouse.TYPE_CODES)

        sort_by = request.args.get("sort_by", "updated_type")
        order_by = request.args.get("order_by", -1, type=int)
        per_page = request.args.get(CommonParams.PER_PAGE, default=20, type=int)
        after_token = request.args.get("after_token", default=None)

        popup_templates, new_after_token = PopupTemplateWareHouseModel().get_list_popup_template_warehouse(
            merchant_id,
            account_id,
            search,
            is_favorite,
            branch_codes,
            purpose_codes,
            holiday_codes,
            type_codes,
            sort_by,
            order_by,
            after_token,
            per_page
        )

        data_return = []
        for popup_template in popup_templates:
            if is_favorite:
                popup_template['is_favorite'] = True
            else:
                popup_template_id = str(popup_template['_id'])
                popup_template_favorite = PopupTemplateFavoritesModel().get_popup_template_favorite_by_popup_template_id(
                    account_id,
                    merchant_id,
                    popup_template_id)
                popup_template["is_favorite"] = True if popup_template_favorite else False
            result = self._convert_detail_popup_template(popup_template, type="get_list")
            data_return.append(result)
        return {
            "data": data_return,
            "paging": {"cursors": {"after_token": new_after_token, "before_token": after_token}}
        }


    def get_detail_popup_template_warehouse(self, popup_template_id):
        self.validate_format_object_id(popup_template_id)
        merchant_id = MobioAdminSDK().get_value_from_token('merchant_id')
        account_id = MobioAdminSDK().get_value_from_token('id')
        detail_popup_template = PopupTemplateWareHouseModel().get_popup_template_warehouse_by_id(popup_template_id)
        if not detail_popup_template:
            raise CustomError(self.lang.get(LANG.POPUP_TEMPLATE_WAREHOUSE_NOT_EXIST).get('message'))
        popup_template_favorite = PopupTemplateFavoritesModel().get_popup_template_favorite_by_popup_template_id(
            account_id,
            merchant_id,
            popup_template_id)
        detail_popup_template["is_favorite"] = True if popup_template_favorite else False
        result = self._convert_detail_popup_template(detail_popup_template)
        return {
            "data": result
        }

    def delete_popup_template_warehouse(self, popup_template_id):
        self.validate_format_object_id(popup_template_id)
        popup_template_warehouse = PopupTemplateWareHouseModel().get_popup_template_warehouse_by_id(popup_template_id)
        if not popup_template_warehouse:
            raise CustomError(self.lang.get(LANG.POPUP_TEMPLATE_WAREHOUSE_NOT_EXIST).get("message"))
        delete_options = {
            CommonParams.ID: ObjectId(popup_template_id)
        }
        status_delete = PopupTemplateWareHouseModel().delete_one(delete_options)
        if status_delete:
            return
        raise CustomError()
