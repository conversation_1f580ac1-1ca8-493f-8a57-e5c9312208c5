#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TruongCL
    Company: MobioVN
    Date created: 05/01/2022
"""
from flask import Blueprint

from src.apis import try_catch_error, build_response_message, HTTP, auth
from src.apis.uri import URI
from src.controllers.v1_0.popup_template_warehouse_controller import PopupTemplateWareHouseController

popup_template_warehouse_service = Blueprint('popup_template_warehouse_service', __name__)


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.SAMPLE_LIBRARY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def add_popup_template_warehouse():
    return build_response_message(PopupTemplateWareHouseController().create_popup_template_warehouse())

