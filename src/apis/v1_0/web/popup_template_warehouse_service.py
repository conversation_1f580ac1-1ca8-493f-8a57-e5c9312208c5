#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""

from flask import Blueprint

from src.apis import try_catch_error, build_response_message, HTTP, auth
from src.apis.uri import URI
from src.controllers.v1_0.popup_template_warehouse_controller import PopupTemplateWareHouseController

popup_template_warehouse_service = Blueprint('popup_template_warehouse_service', __name__)


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.BRANCH, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_branch():
    return build_response_message(PopupTemplateWareHouseController().get_list_branch())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.PURPOSE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_purpose():
    return build_response_message(PopupTemplateWareHouseController().get_list_purpose())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.HOLIDAY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_holiday():
    return build_response_message(PopupTemplateWareHouseController().get_list_holiday())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.TYPES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_types():
    return build_response_message(PopupTemplateWareHouseController().get_list_types())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.SAMPLE_LIBRARY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def add_popup_template_warehouse():
    return build_response_message(PopupTemplateWareHouseController().create_popup_template_warehouse())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.SAMPLE_LIBRARY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_popup_template_warehouse():
    return build_response_message(PopupTemplateWareHouseController().get_list_popup_template_warehouse())


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.DETAIL_POPUP_TEMPLATE_SAMPLE_LIBRARY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_detail_popup_template_warehouse(popup_template_id):
    return build_response_message(PopupTemplateWareHouseController().get_detail_popup_template_warehouse(popup_template_id))


@popup_template_warehouse_service.route(URI.PopupTemplateWarehouse.DETAIL_POPUP_TEMPLATE_SAMPLE_LIBRARY, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_popup_template_warehouse(popup_template_id):
    return build_response_message(PopupTemplateWareHouseController().delete_popup_template_warehouse(popup_template_id))

