#!/usr/bin/env python
# -*- coding: utf-8 -*-
""" Company: MobioVN
    Date created: 2021/11/02
"""
from mobio.sdks.base.apis.check_service import checking_service_mod

from src.apis import app
from src.apis.v1_0.web.asset_service import asset_service_mod
from src.apis.v1_0.web.category_popup_template_service import (
    category_popup_template_service,
)
from src.apis.v1_0.web.color_service import color_service_mod
from src.apis.v1_0.web.email_template_service import email_template_service
from src.apis.v1_0.web.email_template_service_old import email_template_service_old
from src.apis.v1_0.web.font_default_service import font_default_service
from src.apis.v1_0.web.font_template_service import font_template_service
from src.apis.v1_0.web.library_service import library_service_mod
from src.apis.v1_0.web.license_service import license_service_mod
from src.apis.v1_0.web.objects_template_service import objects_template_service
from src.apis.v1_0.web.popop_report_service import popup_report_service
from src.apis.v1_0.web.popup_buillder_service import popup_builder_service
from src.apis.v1_0.web.popup_draft_service import popup_draft_service
from src.apis.v1_0.web.popup_template_service import popup_template_service
from src.apis.v1_0.web.site_category_service import site_category_service_mod
from src.apis.v1_0.web.site_service import site_service_mod
from src.apis.v1_0.web.popup_template_warehouse_service import popup_template_warehouse_service

v1_0_prefix = "/template/api/v1.0"
v1_0_prefix_old = "/api/v1.0"

app.register_blueprint(checking_service_mod, url_prefix=v1_0_prefix)
app.register_blueprint(email_template_service, url_prefix=v1_0_prefix)
app.register_blueprint(popup_template_service, url_prefix=v1_0_prefix)
app.register_blueprint(category_popup_template_service, url_prefix=v1_0_prefix)
app.register_blueprint(popup_builder_service, url_prefix=v1_0_prefix)
app.register_blueprint(popup_draft_service, url_prefix=v1_0_prefix)
app.register_blueprint(objects_template_service, url_prefix=v1_0_prefix)
app.register_blueprint(font_template_service, url_prefix=v1_0_prefix)
app.register_blueprint(popup_report_service, url_prefix=v1_0_prefix)
app.register_blueprint(font_default_service, url_prefix=v1_0_prefix)
app.register_blueprint(library_service_mod, url_prefix=v1_0_prefix)
app.register_blueprint(asset_service_mod, url_prefix=v1_0_prefix)
app.register_blueprint(site_category_service_mod, url_prefix=v1_0_prefix)
app.register_blueprint(popup_template_warehouse_service, url_prefix=v1_0_prefix)

app.register_blueprint(site_service_mod, url_prefix=v1_0_prefix)
app.register_blueprint(license_service_mod, url_prefix=v1_0_prefix)

# email marketing old version
app.register_blueprint(email_template_service_old, url_prefix=v1_0_prefix)
app.register_blueprint(color_service_mod, url_prefix=v1_0_prefix)
