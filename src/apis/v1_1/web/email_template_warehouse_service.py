#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TruongCL
    Company: MobioVN
    Date created: 03/11/2021
"""
from flask import Blueprint

from src.apis import try_catch_error, build_response_message, HTTP, auth
from src.apis.uri import URI
from src.controllers.v1_1.email_template_warehouse_controller import EmailTemplateWareHouseController


email_template_warehouse_service = Blueprint('email_template_warehouse_service', __name__)

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.BRANCH, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_email_template_branch():
    return build_response_message(EmailTemplateWareHouseController().get_list_email_template_branch())

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.PURPOSE, methods=[HTTP.METHOD.GET])    
@auth.verify_token
@try_catch_error
def get_list_email_template_purpose():
    return build_response_message(EmailTemplateWareHouseController().get_list_email_template_purpose())

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.HOLIDAY, methods=[HTTP.METHOD.GET])        
@auth.verify_token
@try_catch_error
def get_list_email_template_holiday():
    return build_response_message(EmailTemplateWareHouseController().get_list_email_template_holiday())

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.LIST_SAMPLE_LIBRARY, methods=[HTTP.METHOD.GET])        
@auth.verify_token
@try_catch_error
def get_list_email_template_sample_library():
    return build_response_message(EmailTemplateWareHouseController().get_list_email_template_sample_library()) 

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.EMAIL_TEMPLATE_TO_SAMPLE_LIBRARY, methods=[HTTP.METHOD.PUT])  
@auth.verify_token
@try_catch_error
def move_email_template_to_sample_library():
    return build_response_message(EmailTemplateWareHouseController().copy_email_template_to_sample_library())

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.DETAIL_EMAIL_TEMPLATE_SAMPLE_LIBRARY, methods=[HTTP.METHOD.GET])   
@auth.verify_token
@try_catch_error
def get_detail_email_template_sample_library(email_id):
    return build_response_message(EmailTemplateWareHouseController().get_detail_email_template_sample_library(email_id))

@email_template_warehouse_service.route(URI.EmailTemplateWareHouse.COUNT_EMAIL_TEMPLATE_SAMPLE_LIBRARY, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def count_email_template_sample_library():
    return build_response_message(EmailTemplateWareHouseController().count_email_template_sample_library())  
