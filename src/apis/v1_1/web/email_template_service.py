#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TruongCL
    Company: MobioVN
    Date created: 03/11/2021
"""
from flask import Blueprint

from src.apis import try_catch_error, build_response_message, HTTP, auth
from src.apis.uri import URI
from src.controllers.v1_1.email_template_controller import EmailTemplateController

email_template_service_v1_1 = Blueprint('email_template_service_v1_1', __name__)


@email_template_service_v1_1.route(URI.EmailTemplateV1_1.EMAIL_TEMPLATE_FAVORITE, methods=[HTTP.METHOD.PUT])    
@auth.verify_token
@try_catch_error
def update_email_template_favorite():
    return build_response_message(EmailTemplateController().update_email_template_favorite())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.TEMPLATES, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def add_email_template():
    return build_response_message(EmailTemplateController().add_email_template())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.TEMPLATES, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_list_email_template():
    return build_response_message(EmailTemplateController().get_list_email_template())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.DETAIL_TEMPLATE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def get_detail_email_template(template_id):
    return build_response_message(EmailTemplateController().get_detail_email_template(template_id))

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.DETAIL_TEMPLATE, methods=[HTTP.METHOD.PATCH])
@auth.verify_token
@try_catch_error
def update_email_template(template_id):
    return build_response_message(EmailTemplateController().update_email_template(template_id))

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.TEMPLATES, methods=[HTTP.METHOD.DELETE])
@auth.verify_token
@try_catch_error
def delete_email_template():
    return build_response_message(EmailTemplateController().delete_email_template())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.COUNT_EMAIL_TEMPLATE_CUSTOM, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def count_email_template():
    return build_response_message(EmailTemplateController().count_email_template())


@email_template_service_v1_1.route(URI.EmailTemplateV1_1.CHECK_NAME_IS_EXISTS, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def check_email_builder_name_exist():
    return build_response_message(EmailTemplateController().check_email_builder_name_exist())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.UPLOAD_IMAGE, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def upload_image_email_builder():
    return build_response_message(EmailTemplateController().upload_image_email_builder())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.SEND_TEST_EMAIL, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def send_test_email():
    return build_response_message(EmailTemplateController().send_test_email())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.COUNT_EMAIL_TEMPLATE_BY_CATEGORY, methods=[HTTP.METHOD.POST])
@auth.verify_token
@try_catch_error
def count_email_template_by_categories():
    return build_response_message(EmailTemplateController().count_email_template_by_category())

@email_template_service_v1_1.route(URI.EmailTemplateV1_1.COUNT_ALL_EMAIL_TEMPLATE, methods=[HTTP.METHOD.GET])
@auth.verify_token
@try_catch_error
def count_all_email_template():
    return build_response_message(EmailTemplateController().count_all_email_template())