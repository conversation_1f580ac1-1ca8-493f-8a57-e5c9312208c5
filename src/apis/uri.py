class URI:
    class EmailTemplate:
        TEMPLATES = "/emails/templates"
        UPLOAD_TEMPLATE = "/emails/templates/actions/upload-template"
        MIGRATION_TEMPLATE = "/emails/templates/actions/migrate-data"
        DELETE_TEMPLATE = "/emails/templates/actions/delete-all"
        GET_DETAIL_TEMPLATE = "/emails/templates/<template_id>"
      
    class EmailTemplateV1_1:
        TEMPLATES = "/emails-builder"
        UPLOAD_TEMPLATE = "/emails-builder/actions/upload-template"
        DETAIL_TEMPLATE = "/emails-builder/<template_id>"
        BRANCH = "/emails-builder/branch"
        PURPOSE = "/emails-builder/purpose"
        HOLIDAY = "/emails-builder/holiday"
        COUNT_EMAIL_TEMPLATE_CUSTOM = "/emails-builder/actions/count"
        EMAIL_TEMPLATE_FAVORITE = "/emails-builder/favorite"
        CHECK_NAME_IS_EXISTS = "/emails-builder/actions/check-name-is-exists"
        UPLOAD_IMAGE = "/emails-builder/actions/upload-image"
        SEND_TEST_EMAIL = "/emails-builder/actions/send-test-email"
        COUNT_EMAIL_TEMPLATE_BY_CATEGORY = "/emails-builder/actions/count/categories"
        COUNT_ALL_EMAIL_TEMPLATE = "/emails-builder/actions/count-all"
        
    class EmailTemplateWareHouse:
        BRANCH = "/emails-builder-warehouse/branch"
        PURPOSE = "/emails-builder-warehouse/purpose"
        HOLIDAY = "/emails-builder-warehouse/holiday"
        LIST_SAMPLE_LIBRARY = "/emails-builder-warehouse/sample-library"
        DETAIL_EMAIL_TEMPLATE_SAMPLE_LIBRARY = "/emails-builder-warehouse/sample-library/<email_id>"
        EMAIL_TEMPLATE_TO_SAMPLE_LIBRARY = "/emails-builder-warehouse/actions/copy-to-sample-library"
        COUNT_EMAIL_TEMPLATE_SAMPLE_LIBRARY = "/emails-builder-warehouse/actions/count"
        SAMPLE_LIBRARY = "/emails-builder-warehouse/sample-library"

    class EmailTemplateOld:
        TEMPLATES = "/merchants/<merchant_id>/marketing/email-channel/templates"
        DETAIL_TEMPLATE = (
            "/merchants/<merchant_id>/marketing/email-channel/templates/<template_id>"
        )
        GET_ALL_TEMPLATE = "/marketing/email-channel/templates"

    class PopupTemplate:
        TEMPLATES = "/popups/templates"
        DELETE_LIST = "/popups/templates/actions/delete-list"
        UPLOAD_TEMPLATES = "/popups/templates/actions/upload-template-default"
        DELETE_POPUP_TEMPLATE_DEFAULT = (
            "/popups/templates/actions/delete-template-default"
        )
        UPDATE_APPLIER_TYPE = (
            "/popups/templates/<template_id>/actions/update-applier-type"
        )
        DETAIL_TEMPLATE = "/popups/templates/<template_id>"
        CHECK_TITLE_IS_EXISTS = "/popups/templates/actions/check-title-is-exists"

    class Categories:
        CATEGORIES = "/categories"
        UPLOAD_CATEGORIES = "/categories/actions/upload-categories-default"
        DELETE_CATEGORIES = "/categories/actions/delete-categories-default"
        GET_DETAIL_CATEGORY = "/categories/<category_id>"

    class PopupBuilder:
        POPUP = "/popups"
        GET_DETAIL_POPUP = "/popups/<popup_builder_id>"
        ACTIVE_IN_LIST = "/popups/<popup_builder_id>/actions/active"
        ACTIVE_IN_LIST_JSON = "/popups/<popup_builder_id>/actions/active/json"
        DELETE_DRAFT = "/popups/<popup_builder_id>/actions/delete-draft"
        EDIT_MAIN = "/popups/<popup_builder_id>/actions/edit-with-main"
        CONNECT_WITH_MANY_POPUP = "/popups/actions/connect-with"
        CONNECT_WITH_POPUP = "/popups/<popup_builder_id>/actions/connect-with"
        DISCONNECT_WITH_POPUP = "/popups/<popup_builder_id>/actions/disconnect-with"
        UPDATE_AND_ACTIVE_POPUP = "/popups/<popup_builder_id>/actions/active-and-update"
        UPDATE_TITLE = "/popups/<popup_builder_id>/actions/update-title"
        COPY_POPUP_BUILDER = "/popups/<popup_builder_id>/actions/copy"
        LIST_ID = "/popups/actions/get-list-id"
        LIST_NAME = "/popups/actions/get-list-name"
        GET_BY_IDS = "/popups/actions/get-by-ids"
        CHECK_IS_USED = "/popups/<popup_builder_id>/actions/check-is-used"
        CHECK_TITLE_IS_EXISTS = "/popups/actions/check-title-is-exists"
        CHECK_LICENSE_EXPORT = "/popups/actions/check-license-export"

    class PopupDraft:
        POPUP = "/popups/drafts"
        GET_DETAIL_POPUP_DRAFT = "/popups/drafts/<popup_draft_id>"

    class Objects:
        OBJECTS = "/<objects>/templates"
        UPLOAD_OBJECTS = "/objects/templates/actions/upload-template-default"
        DELETE_OBJECTS = "/objects/templates/actions/delete-template-default"
        GET_DETAIL_OBJECTS = "/<objects>/templates/<objects_template_id>"

    class Font:
        FONTS = "/fonts/templates"
        UPLOAD_FONTS_GENERAL = "/fonts/templates/actions/upload-font-general"
        DELETE_FONTS_GENERAL = "/fonts/templates/actions/delete-font-general"
        GET_DETAIL_FONT = "/fonts/templates/<font_template_id>"

    class Report:
        WIDGET_1 = "/popups/<popup_builder_id>/reports/widget-1"
        WIDGET_2 = "/popups/<popup_builder_id>/reports/widget-2"
        WIDGET_3 = "/popups/<popup_builder_id>/reports/widget-3"
        WIDGET_4 = "/popups/<popup_builder_id>/reports/widget-4"
        WIDGET_5 = "/popups/<popup_builder_id>/reports/widget-5"
        WIDGET_6 = "/popups/<popup_builder_id>/reports/widget-6"
        WIDGET_7 = "/popups/<popup_builder_id>/reports/widget-7"
        WIDGET_8 = "/popups/<popup_builder_id>/reports/widget-8"
        PROFILE = "/popups/<popup_builder_id>/reports/profile"
        DOWNLOAD_PROFILE = "/popups/<popup_builder_id>/reports/actions/download-profile"
        GET_DATA_CONFIG_FIELD = (
            "/popups/<popup_builder_id>/reports/actions/get-data-config-field"
        )
        DOWNLOAD_SUMMARY = "/popups/<popup_builder_id>/reports/actions/download-summary"
        DATA_FOR_FILTER = (
            "/popups/<popup_builder_id>/reports/actions/get-data-for-filter"
        )

        # Change API for widget 1 - 8
        PROFILE_FILL_IN_THE_FORM = (
            "/popups/<popup_builder_id>/reports/profile-fill-in-the-form"
        )
        OVERVIEW = "/popups/<popup_builder_id>/reports/overview"
        POPUP_CLOSES = "/popups/<popup_builder_id>/reports/popup-closes"
        EFFECTIVE_APPROACH = "/popups/<popup_builder_id>/reports/effective-approach"
        EFFECTIVE_INTERACTION = (
            "/popups/<popup_builder_id>/reports/effective-interaction"
        )
        EFFECTIVENESS_APPLIED_TO_EACH_CHANNEL = (
            "/popups/<popup_builder_id>/reports/effectiveness-applied-to-each-channel"
        )
        WEB_PUSH_CHANNEL_VIEWS_BY_DEVICE = (
            "/popups/<popup_builder_id>/reports/web-push-channel-views-by-device"
        )
        BUTTON_CLICKS_ON_WEB_PUSH_CHANNEL_BY_DEVICE = "/popups/<popup_builder_id>/reports/button-clicks-on-web-push-channel-by-device"

    class FontDefault:
        FONT_DEFAULT = "/font-default"
        UPLOAD_FONT_DEFAULT = "/font-default/actions/upload-font-default"
        DELETE_FONT_DEFAULT = "/font-default/actions/delete-font-default"

    class Library:
        CREATE_LIBRARY = "/libraries"
        LIST_LIBRARY = "/libraries"
        READ_LIBRARY = "/libraries/<library_id>"
        UPDATE_LIBRARY = "/libraries/<library_id>"
        DELETE_LIBRARY = "/libraries/<library_id>"

        LIBRARY_ACTION_CLEAN_ASSETS = "/libraries/<library_id>/assets/actions/clean"
        LIBRARY_ACTION_CLEAN_FONTS = "/libraries/<library_id>/fonts/actions/clean"
        LIBRARY_ACTION_CLEAN_COLORS = "/libraries/<library_id>/colors/actions/clean"

    class Asset:
        CREATE_ASSET = "/assets"
        LIST_ASSET = "/assets"
        READ_ASSET = "/assets/<asset_id>"
        UPDATE_ASSET = "/assets/<asset_id>"
        DELETE_ASSET = "/assets/<asset_id>"

    class SiteCategory:
        LIST_CATEGORY = "/sites/categories"
        INSIGHT_SITE_AMOUNT = "/sites/categories/<category_id>/insights/site-amount"

    class Site:
        CREATE_SITE = "/sites"
        LIST_SITE = "/sites"
        DETAIL_SITE = "/sites/<site_id>"
        UPDATE_SITE = "/sites/<site_id>"
        DELETE_SITE = "/sites/<site_id>"

        IMPORT_SITE = "/sites/actions/import"

    class License:
        CHECK_FEATURE = "license/check-feature"

    class Color:
        COLOR_DEFAULT = "/colors-default"
        COLOR_CUSTOM_BY_FILTER_RESOURCE = "/colors-custom/actions/filter-by-resource"
        ADD_COLOR_CUSTOM = "/colors-custom"

    class EmailCategories:
        EMAIL_CATEGORIES = "emails-builder/categories"
        UPLOAD_EMAIL_CATEGORIES_DEFAULT = "emails-builder/categories/actions/upload-default"
        DELETE_EMAIL_CATEGORIES_DEFAULT = "emails-builder/categories/actions/delete-default"
        DETAIL_EMAIL_CATEGORY = "email-builder/categories/<category_id>"
        
    class EmailSection:
        EMAIL_SECTION = "/emails-builder/section"
        DETAIL_EMAIL_SECTION = "/emails-builder/section/<section_id>"    
    


class MobioConnect:
    ADM_CONFIG = "adm_config"
    ADM_GET_SUB_BRANDS = "adm_get_sub_brands"
    ADM_GET_USER_INFO = "adm_get_user_info"

    USER_EVENT_DEAL_INIT = "user_event_deal_init"
    USER_EVENT_DEAL_CHANGE_STATUS = "user_event_deal_change_status"
    USER_EVENT_DEAL_ASSIGNMENT = "user_event_deal_assignment"

    TICKET_ADD_MANY_TICKETS_TO_DEAL = "ticket_add_many_tickets_to_deal"
    TICKET_REMOVE_MANY_TICKETS_FROM_DEAL = "ticket_remove_many_tickets_from_deal"


class UriJourneyBuilder:
    GET_NAME_MASTER_CAMPAIGN = "get_name_master_campaign"
    GET_NODE_NAME_BY_ID = "get_node_name_by_id"
    CHECK_MASTER_CAMPAIGN_IS_IN_USE = "check_master_campaign_is_in_use"


class UriNotifyManagement:
    SEND_EMAIL_TEST = "send_email_test"
