#!/usr/bin/python
# -*- coding: utf8 -*-
import json
import uuid

from mobio.libs.kafka_lib.helpers.kafka_producer_manager import KafkaProducerManager
from mobio.libs.logging import MobioLogging

from configs.kafka_config import KafkaTopic


class HandleQueue:

    @staticmethod
    def push_tags_event_to_e72_queue(message):
        try:
            message_data = json.dumps(message).encode('utf8')
            MobioLogging().info("push_tags_event_to_e72_queue :: message : %s" % message_data)
            KafkaProducerManager().flush_message(topic=KafkaTopic.E72_TAGS_INTERACTIVE, key=str(uuid.uuid4()),
                                                 value=message)
        except Exception as error:
            MobioLogging().error("Util:: push_tags_event_to_e72_queue: Error {}".format(error))

    @staticmethod
    def push_profile_to_profiling_queue(message):
        try:
            message_data = json.dumps(message).encode('utf8')
            MobioLogging().info("push_profile_to_profiling_queue :: message : %s" % message_data)
            KafkaProducerManager().flush_message(
                topic=KafkaTopic.PROFILING_DEVICE_CONVERT_TO_PROFILE,
                key=str(uuid.uuid4()),
                value=message)
        except Exception as error:
            MobioLogging().error("Util:: push_profile_to_profiling_queue: Error {}".format(error))

    @staticmethod
    def data_push_workflow_queue(topic, message):
        try:
            message_data = json.dumps(message).encode('utf8')
            MobioLogging().info("data_push_workflow_queue :: message : %s" % message_data)
            KafkaProducerManager().flush_message(
                topic=topic,
                key=str(uuid.uuid4()),
                value=message
            )
        except Exception as error:
            MobioLogging().error("Util:: data_push_workflow_queue: Error {}".format(error))
