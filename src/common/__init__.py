#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

from mobio.libs.caching import <PERSON><PERSON><PERSON><PERSON>, StoreType
from mobio.libs.monitor import Monitor

from configs import MobioTemplateApplicationConfig, RedisConfig

WORKING_DIR = MobioTemplateApplicationConfig.WORKING_DIR
APP_DATA_DIR = (
    MobioTemplateApplicationConfig.APPLICATION_DATA_DIR
    + "/"
    + MobioTemplateApplicationConfig.MOBIO_TEMPLATE_FOLDER_NAME
)
APP_LOG_DIR = (
    MobioTemplateApplicationConfig.APPLICATION_LOGS_DIR
    + "/"
    + MobioTemplateApplicationConfig.MOBIO_TEMPLATE_FOLDER_NAME
)
APP_CONFIG_FILE_PATH = os.path.join(
    MobioTemplateApplicationConfig.CONFIG_DIR, "mobio_template.conf"
)
APP_LOG_CONFIG_FILE_PATH = os.path.join(
    MobioTemplateApplicationConfig.CONFIG_DIR, "logging.conf"
)
APP_LOG_FILE_PATH = APP_LOG_DIR + "/mobio_template.log"
APP_MONITOR_SLOW_LOG_PATH = os.path.join(APP_LOG_DIR, "monitor_logs")
APP_MONITOR_SLOW_LOG_FILE_PATH = os.path.join(
    APP_MONITOR_SLOW_LOG_PATH, "monitor_slow.log"
)
os.makedirs(APP_MONITOR_SLOW_LOG_PATH, exist_ok=True)
STATIC_DIR = os.path.join(MobioTemplateApplicationConfig.WORKING_DIR, "static")

EMK_CONFIG_HTML_TO_IMAGE_BIN_PATH = WORKING_DIR + "/bin/wkhtmltoimage-amd64"
STORAGE = WORKING_DIR + "/storage"

SHARE_FOLDER_STATIC = APP_DATA_DIR + "/static/"
THUMB_UPLOAD_FOLDER = APP_DATA_DIR + "/images/"
SHARE_FOLDER_EXPORT_STATIC = os.path.join(SHARE_FOLDER_STATIC, "export_excel")

os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(SHARE_FOLDER_EXPORT_STATIC, exist_ok=True)
os.makedirs(THUMB_UPLOAD_FOLDER, exist_ok=True)
lru_redis_cache = LruCache(
    store_type=StoreType.REDIS,
    config_file_name=APP_CONFIG_FILE_PATH,
    redis_uri=RedisConfig.REDIS_URI,
    redis_cluster_uri=RedisConfig.REDIS_ACTIVATION_URI,
    redis_type=RedisConfig.REDIS_ACTIVATION_TYPE,
    cache_prefix=RedisConfig.CACHE_PREFIX,
)

monitor = Monitor()
monitor.config(func_threshold=0.1, log_file_name=APP_MONITOR_SLOW_LOG_FILE_PATH)


class SECTION:
    LOGGING = "logging_mode"
    LANG = "lang"
    THIRD_PARTY_SERVICE = "third_party_service"


class LANG:
    KEYS = "keys"
    DEFAULT = "default"
    CUSTOM_ERROR = "custom_error"
    BAD_REQUEST = "bad_request"
    UNAUTHORIZED = "unauthorized"
    NOT_ALLOWED = "not_allowed"
    NOT_FOUND = "not_found"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    VALIDATE_ERROR = "validate_error"
    LANG_ERROR = "lang_error"
    MUST_NOT_EMPTY = "must_not_empty"
    NOT_EXIST = "not_exist"
    ALREADY_EXIST = "already_exist"
    MESSAGE_SUCCESS = "message_success"
    VALIDATE_FORMAT_OBJECT_ID = "validate_format_object_id"

    ADD_EMAIL_TEMPLATE_ERROR = "add_email_template_error"
    CREATE_IMG_ERROR = "create_img_error"
    UPSERT_LINK_IMG_ERROR = "update_link_image_error"
    EMAIL_TEMPLATE_NOT_EXIST = "email_template_not_exist"
    DATA_UPDATE_NOT_EXIST = "data_update_not_exist"
    DELETE_EMAIL_TEMPLATE_ERROR = "delete_email_template_error"
    TYPE_EMAIL_TEMPLATE_NOT_EXIST = "type_email_template_not_exist"
    EMAIL_TEMPLATE_NAME_EXIST = "email_template_name_exist"

    # POPUP TEMPLATE
    ADD_POPUP_TEMPLATE_ERROR = "add_popup_template_error"
    POPUP_TEMPLATE_NOT_EXIST = "popup_template_not_exist"
    DELETE_POPUP_TEMPLATE_ERROR = "delete_popup_template_error"
    TYPE_POPUP_TEMPLATE_NOT_EXIST = "type_popup_template_not_exist"
    SESSION_DISSIMILARITY = "session_dissimilarity"
    NAME_POPUP_TEMPLATE_ALREADY_EXISTS_IN_SYSTEM = (
        "name_popup_template_already_exists_in_system"
    )

    # CATEGORY_POPUP_TEMPLATE
    ADD_CATEGORY_ERROR = "add_category_error"
    CATEGORY_NOT_EXIST = "category_not_exist"
    DELETE_CATEGORY_ERROR = "delete_category_error"

    # POPUP_BUILDER
    ADD_POPUP_BUILDER_ERROR = "add_popup_builder_error"
    POPUP_BUILDER_NOT_EXIST = "popup_builder_not_exist"
    DELETE_POPUP_BUILDER_ERROR = "delete_popup_builder_error"
    POPUP_IS_BEING_USED = "popup_is_being_used"
    PUBLISH_POPUP_BUILDER_ERROR = "publish_popup_builder_error"
    POPUP_BUILDER_IS_LATEST = "popup_builder_is_latest"
    UPDATE_POPUP_DRAFT_ID_TO_POPUP_BUILDER_ERROR = (
        "update_popup_draft_id_to_popup_builder_error"
    )
    POPUP_BUILDER_NOT_ACTIVE = "popup_builder_not_active"
    COPY_POPUP_BUILDER_ERROR = "copy_popup_builder_error"
    NAME_POPUP_BUILDER_ALREADY_EXISTS_IN_SYSTEM = (
        "name_popup_builder_already_exists_in_system"
    )

    # POPUP_DRAFT
    ADD_POPUP_DRAFT_ERROR = "add_popup_draft_error"
    DELETE_POPUP_DRAFT_ERROR = "delete_popup_draft_error"
    POPUP_DRAFT_NOT_EXIST = "popup_draft_not_exist"

    # BUTTON TEMPLATE
    ADD_BUTTONS_TEMPLATE_ERROR = "add_buttons_template_error"
    BUTTONS_TEMPLATE_NOT_EXIST = "buttons_template_not_exist"
    DELETE_BUTTONS_TEMPLATE_ERROR = "delete_buttons_template_error"
    TYPE_BUTTONS_TEMPLATE_NOT_EXIST = "type_buttons_template_not_exist"

    # COUNTER TEMPLATE
    ADD_COUNTERS_TEMPLATE_ERROR = "add_counters_template_error"
    COUNTERS_TEMPLATE_NOT_EXIST = "counters_template_not_exist"
    DELETE_COUNTERS_TEMPLATE_ERROR = "delete_counters_template_error"
    TYPE_COUNTERS_TEMPLATE_NOT_EXIST = "type_counters_template_not_exist"

    # FORM TEMPLATE
    ADD_FORMS_TEMPLATE_ERROR = "add_forms_template_error"
    FORMS_TEMPLATE_NOT_EXIST = "forms_template_not_exist"
    DELETE_FORMS_TEMPLATE_ERROR = "delete_forms_template_error"
    TYPE_FORMS_TEMPLATE_NOT_EXIST = "type_forms_template_not_exist"

    # ICON TEMPLATE
    ADD_ICONS_TEMPLATE_ERROR = "add_icons_template_error"
    ICONS_TEMPLATE_NOT_EXIST = "icons_template_not_exist"
    DELETE_ICONS_TEMPLATE_ERROR = "delete_icons_template_error"
    TYPE_ICONS_TEMPLATE_NOT_EXIST = "type_icons_template_not_exist"

    # FONT TEMPLATE
    ADD_FONT_TEMPLATE_ERROR = "add_font_template_error"
    FONT_TEMPLATE_NOT_EXIST = "font_template_not_exist"
    DELETE_FONT_TEMPLATE_ERROR = "delete_font_template_error"
    TYPE_FONT_TEMPLATE_NOT_EXIST = "type_font_template_not_exist"
    FONT_NAME_ALREADY_EXISTS_IN_SYSTEM = "font_name_already_exists_in_system"
    NAME_ALREADY_EXISTS_IN_FONT_DEFAULT = "name_already_exists_in_font_default"

    # FONT DEFAULT
    ADD_FONT_DEFAULT_ERROR = "add_font_default_error"
    DELETE_FONT_DEFAULT_ERROR = "delete_font_default_error"
    FONT_DEFAULT_NOT_EXIST = "font_default_not_exist"
    MUST_HAVE_FONT = "must_have_font"

    # I18N_KEY CATEGORIES
    I18N_REGISTER = "i18n_register"
    I18N_NOTIFICATIONS = "i18n_notifications"
    I18N_PROMOTION_OFFER = "i18n_promotion_offer"
    I18N_CONTACT = "i18n_contact"
    I18N_APPRECIATION_GIFTS = "i18n_appreciation_gifts"
    I18N_OTHERS = "i18n_others"

    # LIBRARIES
    HAVE_FIELD_NOT_SUPPORT = "have_field_not_support"

    # LICENSE
    LICENSE_NOT_ALLOWED = "license_not_allowed"
    LICENSE_EFFECTIVENESS_APPLIED_TO_EACH_CHANNEL = (
        "license_effectiveness_applied_to_each_channel"
    )

    # CATEGORY_EMAIL_TEMPLATE
    NAME_CATEGORY_EMAIL_BUILDER_ALREADY_EXISTS_IN_SYSTEM = (
        "name_category_email_builder_already_exists_in_system"
    )
    CATEGORY_IS_DEFAULT = "category_is_default"
    NAME_CATEGORY_EMAIL_BUILDER_EMPTY = "name_category_email_builder_empty"

    # EMAIL BUILDER
    EMAIL_BUILDER_ALREADY_EXISTS_IN_SYSTEM = "email_builder_already_exists_in_system"
    NAME_EMAIL_BUILDER_EMPTY = "name_email_builder_empty"
    FILE_NOT_FOUND = "file_not_found"
    
    # SECTION_EMAIL_TEMPLATE
    EMAIL_TEMPLATE_SECTION_NAME_EXIST = "email_template_section_name_exist" 
    EMAIL_TEMPLATE_SECTION_NAME_EMPTY = "email_template_section_name_empty"
    ADD_EMAIL_TEMPLATE_SECTION_ERROR = "add_email_template_section_error"
    UPDATE_EMAIL_TEMPLATE_SECTION_ERROR = "update_email_template_section_error"
    EMAIL_TEMPLATE_SECTION_NOT_EXIST = "email_template_section_not_exist"
    DELETE_EMAIL_TEMPLATE_SECTION_ERROR = "delete_email_template_section_error"
        


class EmailTemplate:
    ID = "_id"
    TITLE = "title"
    BODY_TEMPLATE = "body_template"
    LINK_IMAGE = "link_image"
    NAME = "name"
    VALUE = "value"
    DESCRIPTION = "description"
    EMAIL_TEMPLATE_INFO = "email_template_info"
    CATEGORIES = "categories"
    STATUS = "status"
    BODY = "body"
    NAME_UNSIGNED = "name_unsigned"
    THUMBNAIL = "thumbnail"
    SMALL_THUMBNAIL = "small_thumbnail"
    DESCRIPTION_UNSIGNED = "description_unsigned"
    IS_FAVORITE = "is_favorite"
    BRANCH_CODE = "branch_codes"
    PURPOSE_CODE = "purpose_codes"
    HOLIDAY_CODE = "holiday_codes"



class PopupTemplate:
    TITLE = "title"
    TITLE_CODE = "title_code"
    TITLE_UNIQUE = "title_unique"
    BODY_TEMPLATE = "body_template"
    BUILD_TEMPLATE = "build_template"
    CATEGORY_ID = "category_id"
    SECOND_PAGE = "second_page"
    AVATAR_INFO = "avatar_info"
    URL = "url"
    IMAGE_BASE64 = "image_base64"
    LINK_IMAGE = "link_image"
    BG_INFO = "bg_info"
    LST_INFO_IMAGES = "lst_info_images"
    INFO_IMAGE = "info_image"
    INFO_IMAGES = "info_images"
    LOCAL_PATH = "local_path"
    IS_EXISTS = "is_exists"


class Category:
    NAME = "name"
    PRIORITY = "priority"
    I18N_KEY = "i18n_key"


class CategoryEmail:
    NAME = "name"
    NAME_UNSIGNED = "name_unsigned"


class PopupBuilder:
    ID = "id"
    POPUP_BUILDER_ID = "popup_builder_id"
    POPUP_DRAFT_ID = "popup_draft_id"
    POPUP_TEMPLATE_ID = "popup_template_id"
    TITLE = "title"
    TITLE_CODE = "title_code"
    TITLE_UNIQUE = "title_unique"
    BODY_TEMPLATE = "body_template"
    BUILD_TEMPLATE = "build_template"
    DYNAMIC = "dynamic"
    ACTIVE = "active"
    CATEGORY_ID = "category_id"
    SECOND_PAGE = "second_page"
    POPUP_BUILDER_INFO = "popup_builder_info"
    ENTITY_USING_ID = "entity_using_id"
    CONFIG_FIELD = "config_field"
    CURRENT_FIELD = "current_field"
    CONFIG_BUTTON = "config_button"
    CURRENT_BUTTON = "current_button"
    FIELD_KEY = "field_key"
    FIELD_NAME = "field_name"
    IS_AVAILABLE = "is_available"
    AVAILABLE = "1"
    NOT_AVAILABLE = "0"
    BUTTON_KEY = "button_key"
    BUTTON_NAME = "button_name"
    POPUP_BUILDER_USING = "popup_builder_using"
    JOURNEY_BUILDER_USING = "journey_builder_using"
    MASTER_CAMPAIGN_USING = "master_campaign_using"
    ACTIVATE = "active"
    ACTIVATED = "1"
    NOT_ACTIVATED = "0"
    IS_USED = "is_used"
    IS_EXISTS = "is_exists"
    AVATAR_INFO = "avatar_info"
    URL = "url"
    LOCAL_PATH = "local_path"
    SESSION = "session"
    MERCHANT_ID = "merchant_id"
    VIEW = "view"
    PROFILE = "profile"
    USING_BY = "using_by"
    NUMBER_LINK_TRACKING = (
        "number_link_tracking"  # Số lượng link tracking phục vụ license
    )
    CREATED_BY = "created_by"
    CREATED_TIME = "created_time"
    UPDATED_BY = "updated_by"
    UPDATED_TIME = "updated_time"

    MAX_IDS_REQUEST = 20

    @classmethod
    def get_list_allow_get_by_ids(cls):
        return [
            cls.ID,
            cls.TITLE,
            cls.POPUP_DRAFT_ID,
            cls.POPUP_TEMPLATE_ID,
            cls.BODY_TEMPLATE,
            cls.BUILD_TEMPLATE,
            cls.SESSION,
            cls.MERCHANT_ID,
            cls.AVATAR_INFO,
            cls.POPUP_BUILDER_INFO,
            cls.SECOND_PAGE,
            cls.VIEW,
            cls.PROFILE,
            cls.USING_BY,
            cls.ACTIVATE,
            cls.NUMBER_LINK_TRACKING,
            cls.CREATED_BY,
            cls.CREATED_TIME,
            cls.UPDATED_BY,
            cls.UPDATED_TIME,
            cls.DYNAMIC,
        ]


class PopupDraft:
    POPUP_DRAFT_ID = "popup_draft_id"
    TITLE = "title"
    TITLE_CODE = "title_code"
    BODY_TEMPLATE = "body_template"
    BUILD_TEMPLATE = "build_template"
    DYNAMIC = "dynamic"
    ACTIVE = "active"
    CATEGORY_ID = "category_id"
    SECOND_PAGE = "second_page"
    CURRENT_FIELD = "current_field"
    CURRENT_BUTTON = "current_button"
    AVATAR_INFO = "avatar_info"
    URL = "url"
    LOCAL_PATH = "local_path"

    NUMBER_LINK_TRACKING = (
        "number_link_tracking"  # Số lượng link tracking phục vụ license
    )


class ObjectsTemplate:
    TITLE = "title"
    TITLE_CODE = "title_code"
    BODY_TEMPLATE = "body_template"
    TEMPLATE_TYPE = "template_type"
    ELEMENT_IDS = "element_ids"
    FORM_LAYOUT = "form_layout"
    CATEGORY_ID = "category_id"
    AVATAR_INFO = "avatar_info"
    BG_INFO = "bg_info"
    IMAGE_BASE64 = "image_base64"
    BACKGROUND_IMAGES = "background_images"
    LINK_IMAGE = "link_image"
    URL = "url"
    LOCAL_PATH = "local_path"
    ICONS = "icons"
    FORMS = "forms"
    BUTTONS = "buttons"
    COUNTERS = "counters"
    RESOURCE = "resource"


class FontTemplate:
    FONT_DEFAULT_ID = "font_default_id"
    FONT_TYPE = "font_type"
    FONT_WEIGHT = "font_weight"
    TITLE = "title"
    TITLE_CODE = "title_code"
    URL = "url"
    SUB_FONT_LINK = "sub_font_link"
    URL_INFO = "url_info"
    FILE_NAME = "filename"
    LIGHT = "light"
    NORMAL = "normal"
    BOLD = "bold"
    GOOGLE_FONT = "google_font"
    CUSTOM_FONT = "custom_font"
    SEARCH = "search"
    RESOURCE = "resource"


class FontDefault:
    SEARCH = "search"
    TITLE = "title"
    TITLE_CODE = "title_code"
    URL = "url"
    FONT_WEIGHT = "font_weight"
    MERCHANT_USING = "merchant_using"


class Report:
    EVENT_TYPE = "event_type"
    CHANNEL = "channel"
    POPUP_BUILDER_ID = "popup_builder_id"
    PROFILE_ID = "profile_id"
    JOURNEY_BUILDER_ID = "journey_builder_id"
    JOURNEY_BUILDER = "journey_builder"
    MASTER_CAMPAIGN_ID = "master_campaign_id"
    ID_BUTTON_CLICK = "id_button_click"
    VIEW = "view"
    CLICK = "click"
    CLOSE = "close"
    PUSH_IN_APP = "push_in_app"
    WEB_PUSH = "web_push"
    DEVICE_DESKTOP = "device_desktop"
    DEVICE_MOBILE = "device_mobile"
    ACTION_TIME = "action_time"
    UNIQUE_INDEX = "unique_index"
    ACTION_DATE = "action_date"
    CREATED_TIME = "created_time"
    NAME_CODE = "name_code"
    PRIMARY_PHONE_CODE = "primary_phone_code"
    PRIMARY_EMAIL_CODE = "primary_email_code"
    PROFILE = "profile"
    SUMMARY_PRESENT = "summary_present"
    SUMMARY_BEFORE = "summary_before"
    CONVERSION_RATE = "conversion_rate"
    FIELD_KEY = "field_key"
    FIELD_NAME = "field_name"
    IS_AVAILABLE = "is_available"
    AVAILABLE = "1"
    NOT_AVAILABLE = "0"
    MASTER_CAMPAIGN = "master_campaign"
    MESSAGE = "message"
    MORE_INFO = "more_info"
    URL = "url"


class ReportTextExcel:
    NAME_FILE_SUMMARY = "BCC.xlsx"
    NAME_FILE_LIST_PROFILE = "list_information_profile.xlsx"

    SUMMARY_NAME_SHEET_1 = "BBC - Tổng quan"
    SUMMARY_NAME_SHEET_2 = "BBC - Hiệu quả tương tác"
    SUMMARY_NAME_SHEET_3 = "BBC - Thiết bị"

    LIST_PROFILE_NAME_SHEET_1 = "Danh sách profile điền form"

    LUOT_XEM = "Lượt xem"
    LUOT_BAM_BUTTON = "Lượt bấm button"
    PROFILE_DIEN_FORM = "Profile điền form"
    LUOT_DONG_POPUP = "Lượt đóng popup"
    TI_LE_BAM_BUTTON = "Tỉ lệ bấm button"
    KENH_WEB_PUSH = "Kênh Web push"
    KENH_PUSH_IN_APP = "Kênh Push in-app"
    TONG_QUAN = "Tổng quan"
    WEB_PUSH = "Web push"
    PUSH_IN_APP = "Push in-app"
    DA_XOA = " (Đã xóa)"

    BUTTON = "Button"
    KENH = "Kênh"

    DESKTOP = "Desktop"
    MOBILE = "Mobile"

    MASTER_CAMPAIGN = "Master Campaign"
    JOURNEY_BUILDER = "Journey Builder"
    MESSAGE = "Thông điệp"
    THOI_GIAN_NHAN_PROFILE = "Thời gian nghi nhận profile điền form"
    PROFILE_ID = "Profile ID"


class CommonParams:
    PAGE = "page"
    PER_PAGE = "per_page"
    TOTAL_PAGE = "total_page"
    TOTAL_COUNT = "total_count"
    ID = "_id"
    ACCOUNT_ID = "account_id"
    MERCHANT_ID = "merchant_id"
    CREATED_BY = "created_by"
    CREATED_TIME = "created_time"
    UPDATED_BY = "updated_by"
    UPDATED_TIME = "updated_time"
    SESSION = "session"
    GENERAL = "2"
    PARTICULAR = "3"
    AVATAR_INFO = "avatar_info"
    AVATAR = "avatar"
    APPLIER_TYPE = "applier_type"
    SEARCH = "search"
    AVATAR_LOCAL_PATH = "avatar_info.local_path"
    POPUP_LOCAL_PATH = "popup_builder_info.local_path"
    SINCE = "since"
    UNTIL = "until"
    IDS = "ids"
    LIST_DATA = "list_data"
    FIELDS = "fields"
    PUBLIC = 1
    PRIVATE = 0
    IS_FAVORITE = "is_favorite"
    CATEGORY_IDS = "category_ids"


class History:
    ID = "_id"
    MERCHANT_ID = "merchant_id"
    EVENT_TYPE = "event_type"
    CHANNEL = "channel"
    POPUP_BUILDER_ID = "popup_builder_id"
    PROFILE_ID = "profile_id"
    JOURNEY_BUILDER_ID = "journey_builder_id"
    MASTER_CAMPAIGN_ID = "master_campaign_id"
    ID_BUTTON_CLICK = "id_button_click"
    VIEW = "view"
    CLICK = "click"
    CLOSE = "close"
    PUSH_IN_APP = "push_in_app"
    WEB_PUSH = "web_push"
    DEVICE_DESKTOP = "device_desktop"
    DEVICE_MOBILE = "device_mobile"
    ACTION_TIME = "action_time"
    ACTION_DATE = "action_date"
    EVENT_ID = "event_id"
    UNIQUE_INDEX = "unique_index"
    TAG_ID = "tag_id"


class DB_TABLE_TEMPLATE:
    ID = "id"
    NAME = "name"
    DESCRIPTION = "description"
    VALUE = "value"
    APPLIER = "applier"
    APPLIER_TYPE = "applier_type"
    STATUS = "status"
    USED_NUMBER = "used_number"
    CREATED_DATE = "created_date"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    UPDATED_BY = "updated_by"
    UPDATED_USER = "updated_user"
    MERCHANT_ID = "merchant_id"


class DatabaseName:
    CATEGORIES_POPUP_TEMPLATE = "categories_popup_template"
    EMAIL_TEMPLATES = "email_templates"
    FONT_DEFAULT = "font_default"
    FONT_TEMPLATE = "font_template"
    OBJECTS_TEMPLATE = "objects_template"
    POPUP_BUILDER = "popup_builder"
    POPUP_DRAFT = "popup_draft"
    POPUP_TEMPLATES = "popup_templates"
    PROFILE_INFORMATION = "profile_information"
    INFORMATION_FOR_REPORT = "information_for_report"
    HISTORY = "history"
    SUMMARY_FOR_REPORT = "summary_for_report"
    COLOR = "color"
    EMAIL_BUILDER = "email_builder"
    EMAIL_TEMPLATE_BRANCH = "email_template_branch"
    EMAIL_TEMPLATE_HOLIDAY = "email_template_holiday"
    EMAIL_TEMPLATE_PURPOSE = "email_template_purpose"
    EMAIL_TEMPLATE_FAVORITE = "email_template_favorite"
    EMAIL_TEMPLATE_WAREHOUSE = "email_template_warehouse"
    EMAIL_TEMPLATE_SECTION = "email_template_section"
    


class COMMON:
    UTF8 = "utf-8"
    DATE_TIME_FORMAT_UTC = "%Y-%m-%dT%TZ"
    PAGING_PER_PAGE = 25
    MAXIMUM_PER_PAGE = 50
    CS_CONTACT = "<EMAIL>"


class OrderType:
    ASC = "asc"
    DESC = "desc"

    @staticmethod
    def to_mongo_order(order_type: str):
        if order_type.lower() == OrderType.ASC:
            return 1
        return -1


class Params:
    SORT = "sort"
    ORDER = "order"


class LOGGING:
    WRITE_TRACEBACK_FOR_ALL_CUSTOMIZE_EXCEPTION = (
        "write_traceback_for_all_customize_exception"
    )
    WRITE_TRACEBACK_FOR_GLOBAL_EXCEPTION = "write_traceback_for_global_exception"
    LOG_FOR_REQUEST_SUCCESS = "log_for_request_success"
    LOG_FOR_ALL_CUSTOMIZE_EXCEPTION = "log_for_all_customize_exception"
    LOG_FOR_GLOBAL_EXCEPTION = "log_for_global_exception"
    FILE_MAX_BYTES = "file_max_bytes"
    FILE_BACKUP_COUNT = "file_backup_count"


class Action:
    WRITE = "w"
    READ = "r"
    APPEND = "a"


class LibraryConstant:
    DEFAULT_LIBRARY = "unknow"


class SiteStatus:
    AVAILABLE = "available"
    DELETED = "deleted"


class CONSTANT:
    DEFAULT_PER_PAGE = 25
    MAXIMUM_PER_PAGE = 50

    MOBIO_EDITOR_COPYRIGHT = "MOBIO WEB EDITOR © 2023"


class FontResource:
    POPUP = "popup"
    LANDINGPAGE = "landingpage"
    FORM_BUILDER = "form_builder"


class ConstantTypeColor:
    DEFAULT = "DEFAULT"
    CUSTOM = "CUSTOM"


class ConstantResourceTypeColor:
    FORM_BUILDER = "FORM_BUILDER"
    ALL = [FORM_BUILDER]
    
class DatetimeTypeKeys:
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    FORMAT = "%Y-%m-%dT%H:%M:%S.000Z"
    VALUES = [CREATED_TIME, UPDATED_TIME]


class EmailTemplateSampleLibrary:
    ID = "_id"
    NAME = "name"
    HOLIDAY_CODES = "holiday_codes"
    BRANCH_CODES = "branch_codes"
    PURPOSE_CODES = "purpose_codes"
    BODY = "body"
    THUMBNAIL = "thumbnail"
    SMALL_THUMBNAIL = "small_thumbnail"
    DESCRIPTION = "description"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
    NAME_UNSIGNED = "name_unsigned"
    DESCRIPTION_UNSIGNED = "description_unsigned"
    IS_FAVORITE = "is_favorite"

class SendTestEmail:
    FROM = "from"
    FROM_NAME = "from_name"
    TITLE = "title"
    SEND_TO = "send_to"
    BODY = "body"
    
class SectionEmailTemplate:
    ID = "_id"
    NAME = "name"
    NAME_UNSIGNED = "name_unsigned"
    THUMBNAIL = "thumbnail"
    BODY = "body"
    MERCHANT_ID = "merchant_id"
    CREATED_TIME = "created_time"
    UPDATED_TIME = "updated_time"
    CREATED_BY = "created_by"
    UPDATED_BY = "updated_by"
