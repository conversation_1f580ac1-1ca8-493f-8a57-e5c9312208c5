#!/usr/bin/env python
# -*- coding: utf-8 -*-
import requests
from mobio.libs.Singleton import Singleton
from mobio.sdks.admin import MobioAdminSDK

from configs import Mobio
from src.apis.uri import MobioConnect
from src.common import SECTION, lru_redis_cache
from src.common.system_config import SystemConfig


@Singleton
class MerchantConfig:
    BASIC_TOKEN = "basic_token"
    PROFILING_TOKEN = 'p_t'

    ADMIN_HOST = 'admin-app-api-service-host'
    JOURNEY_BUILDER_APP_INTERNAL_SERVICE_HOST = 'journey-builder-app-internal-service-host'

    def __init__(self):
        self.sys_conf = SystemConfig()
        self.configs = {}
        self.current_merchant = ''

    def get_merchant_config(self, merchant_id):
        if merchant_id not in self.configs:
            # result = self.__request_get_merchant_auth(merchant_id)
            result = self.__request_get_merchant_config_host_by_sdk(merchant_id)
            if result:
                self.configs[merchant_id] = result
        return self.configs[merchant_id]

    # @lru_redis_cache.add_for_class()
    def __request_get_merchant_auth(self, merchant_id):
        headers = {"Authorization": Mobio.MOBIO_TOKEN}
        host = Mobio.ADMIN_HOST
        adm_url = SystemConfig().get_section_map(SECTION.THIRD_PARTY_SERVICE)[MobioConnect.ADM_CONFIG] \
            .format(host=host, merchant_id=merchant_id)

        self.sys_conf.logger.debug('::get_merchant_auth: adm_url = %s' % adm_url)
        response = requests.get(adm_url, headers=headers)
        response.raise_for_status()

        result = response.json()
        self.sys_conf.logger.debug('::get_merchant_auth: result = %s' % result)

        return {
            MerchantConfig.ADMIN_HOST: result['data'].get(MerchantConfig.ADMIN_HOST),
            MerchantConfig.JOURNEY_BUILDER_APP_INTERNAL_SERVICE: result['data'].get(
                MerchantConfig.JOURNEY_BUILDER_APP_INTERNAL_SERVICE),
        }

    @lru_redis_cache.add_for_class()
    def __request_get_merchant_config_host_by_sdk(self, merchant_id):
        merchant_config_host = MobioAdminSDK().request_get_merchant_config_host(
            merchant_id,
            key=None,
        )
        return {
            MerchantConfig.ADMIN_HOST: merchant_config_host[MerchantConfig.ADMIN_HOST],
            MerchantConfig.JOURNEY_BUILDER_APP_INTERNAL_SERVICE_HOST: merchant_config_host[MerchantConfig.JOURNEY_BUILDER_APP_INTERNAL_SERVICE_HOST],
        }

    def set_current_merchant(self, merchant_id):
        self.get_merchant_config(merchant_id)
        if merchant_id != self.current_merchant:
            self.current_merchant = merchant_id

    def get_current_config(self):
        if not self.current_merchant:
            raise Exception("Please set merchant first")
        return self.configs[self.current_merchant]

    def get_current_host(self):
        if not self.current_merchant:
            raise Exception("Please set merchant first")
        configs = self.get_merchant_config(self.current_merchant)

        return configs[MerchantConfig.PROFILING_HOST]

    def get_current_token(self):
        if not self.current_merchant:
            raise Exception("Please set merchant first")
        configs = self.get_merchant_config(self.current_merchant)

        return configs[MerchantConfig.PROFILING_TOKEN]
