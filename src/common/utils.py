#!/usr/bin/env python
# -*- coding: utf-8 -*-
import hashlib
import re
import uuid
from datetime import datetime

import pymongo
from bson import ObjectId
from flask import request


def get_request_id():
    url = request.url
    request_time = datetime.utcnow()
    identify = url + str(request_time)
    identify = identify.encode('utf-8')
    return hashlib.md5(identify).hexdigest()


def json_compare(json_old, json_new):
    if not json_old:
        return {
            'before': None,
            'after': json_new
        }
    list_key_old = json_old.keys()
    list_key_new = json_new.keys()

    # Lay danh sach key khong co
    compare_list_old = list(set(list_key_old) - set(list_key_new))
    compare_list_new = list(set(list_key_new) - set(list_key_old))

    before_data = {}
    for old in compare_list_old:
        before_data[old] = json_old.get(old)

    after_data = {}
    for new in compare_list_new:
        after_data[new] = json_new.get(new)

    # Danh sach cac key co trong 2 mang
    list_intersection = [k for k in list_key_old if k in list_key_new]
    list_change = []
    for key in list_intersection:
        if json_old.get(key) != json_new.get(key):
            list_change.append(key)

    for key_change in list_change:
        before_data[key_change] = json_old.get(key_change)
        after_data[key_change] = json_new.get(key_change)

    return {
        'before': before_data,
        'after': after_data
    }


patterns = {
    '[àáảãạăắằẵặẳâầấậẫẩ]': 'a',
    '[đ]': 'd',
    '[èéẻẽẹêềếểễệ]': 'e',
    '[ìíỉĩị]': 'i',
    '[òóỏõọôồốổỗộơờớởỡợ]': 'o',
    '[ùúủũụưừứửữự]': 'u',
    '[ỳýỷỹỵ]': 'y'
}

pattern_valid_phone_number = "^(\+?84|0)(1[2689]|[89])[0-9]{8}$"
DATE_TIME_FORMAT_UTC = "%Y-%m-%dT%H:%M:%SZ"


def utf8_to_ascii(text):
    if text is None:
        return ''
    output = text
    for regex, replace in patterns.items():
        output = re.sub(regex, replace, output)
        # deal with upper case
        output = re.sub(regex.upper(), replace.upper(), output)
    return output


def convert_data_for_search_regex(str_data):
    result = re.sub(r"([\.\*\+\?\^\$\{\}\(\)\|\[\]])", r"\\\1", str_data)
    return result


def generate_session():
    return str(uuid.uuid4())


def get_utcnow():
    return str(datetime.utcnow())


def generate_object_id():
    return str(ObjectId())


def mongo_generate_after_token(data, per_page, sort_order: dict):
    if not data or not isinstance(data, list) or len(data) < per_page:
        return ''
    if not sort_order:
        return ''
    sort_keys = sort_order.keys()
    if not sort_order.keys():
        return ''
    tmp = data.copy()
    last_item = tmp.pop()
    del tmp
    sort_key = ''
    for key in sort_keys:
        sort_key = key
        break
    if not sort_key:
        return ''
    order = sort_order.get(sort_key)
    if sort_key not in last_item:
        return ''
    last_value = last_item.get(sort_key)
    last_id = last_item.get('_id', last_item.get('id'))
    from mobio.sdks.admin.utils import Base64
    condition_next_page = "{}{}{}#{}".format(order, last_id, last_value, type(last_value).__name__)
    return Base64.encode(condition_next_page)


def mongo_parse_after_token(after_token, sort_order: dict):
    if not after_token:
        return {}
    if not sort_order:
        return {}
    sort_keys = sort_order.keys()
    if not sort_order.keys():
        return {}
    sort_key = ''
    for key in sort_keys:
        sort_key = key
        break
    try:
        from mobio.sdks.admin.utils import Base64
        data = Base64.decode(after_token)
        operator = int(data[0:2]) if data.startswith('-') else int(data[0])

        last_id = data[1:25] if operator == pymongo.ASCENDING else data[2:26]
        last_value = data[25:data.index('#')] if operator == pymongo.ASCENDING else data[26:data.index('#')]
        operator = "$gt" if operator == pymongo.ASCENDING else "$lt"
        data_type = data[data.index('#') + 1:]

        from pydoc import locate
        t = locate(data_type)
        condition_next_page = {
            "$or": [
                {sort_key: {operator: t(last_value)}},
                {"$and": [{sort_key: t(last_value)}, {"_id": {operator: ObjectId(last_id)}}]}
            ]
        }
        return condition_next_page
    except:
        return {}


def separate_string_by_comma(str_input) -> list:
    if not str_input:
        return []
    return [element.strip() for element in str_input.split(',') if element.strip()]
