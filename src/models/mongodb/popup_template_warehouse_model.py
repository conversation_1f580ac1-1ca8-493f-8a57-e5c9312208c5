#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from src.common import DatabaseName, DatetimeTypeKeys
from src.models.mongodb import BaseModel
from datetime import datetime
from bson.objectid import ObjectId
from mobio.libs.logging import Mo<PERSON>Logging
from pymongo import ASCENDING, IndexModel, DESCENDING

from src.models.popup_template_favoirites_model import PopupTemplateFavoritesModel


class PopupTemplateWareHouseModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_TEMPLATE_WAREHOUSE
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def sync_table(self):
        try:
            # self.conn_primary.drop_indexes()
            index_1 = IndexModel([
                ("title", ASCENDING),
            ],
                background=True,
                unique=True,
            )
            index_2 = IndexModel([
                ("_id", ASCENDING),
                ("title", ASCENDING),
                ("title_unique", DESCENDING),
            ],
                background=True,
                unique=False,
            )
            index_3 = IndexModel([
                ("branch_codes", ASCENDING),
            ],
                background=True,
                unique=False,
            )
            index_4 = IndexModel([
                ("holiday_codes", ASCENDING),
            ],
                background=True,
                unique=False,
            )
            index_5 = IndexModel([
                ("purpose_codes", ASCENDING),
            ],
                background=True,
                unique=False,
            )

            self.conn_primary.create_indexes([index_1, index_2, index_3, index_4, index_5])
            print(
                "{} result: {}".format(
                    self.collection_name, self.conn_primary.index_information()
                )
            )
        except Exception as er:
            err_msg = "sync_table Mongo, ColorModel, ERR: {}".format(er)
            print(err_msg)

    def find_paginate_load_more(
            self,
            search_option={},
            per_page=20,
            after_token=None,
            sort_option=[("_id", -1)],
            projection=None,
            list_field_datetime=DatetimeTypeKeys.VALUES,
    ):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        sort_key = sort_option[0][0]
        sort_value = sort_option[0][1]
        sort_filter = {}
        # Check token
        if after_token:
            operator_query = "$lt" if sort_value == -1 else "$gt"
            token = self.parse_token(after_token)
            field = token.get("field")
            last_id = token.get("last_id")
            if "key" in field and "value" in field:
                key_sorted = field["key"]
                value_sorted = field["value"]
                if key_sorted in list_field_datetime:
                    value_sorted = datetime.fromtimestamp(float(value_sorted))

                try:
                    last_id = ObjectId(last_id)
                except:
                    last_id = int(last_id)

                item_sort_filter = [
                    {key_sorted: {operator_query: value_sorted}},
                    {key_sorted: value_sorted, "_id": {operator_query: last_id}},
                ]

                if "$or" in search_option:
                    sort_filter = {
                        "$or": [
                            {"$and": [cond1, cond2]} for cond1 in search_option["$or"] for cond2 in item_sort_filter
                        ]
                    }
                else:
                    sort_filter = {"$or": item_sort_filter}
        find_filter = {**search_option, **sort_filter}
        MobioLogging().info("find_paginate_load_more :: find_filter :: {}".format(find_filter))
        collection = self.find(find_filter, projection).limit(per_page)
        sort_option.append(("_id", sort_value))
        collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = BaseModel.generate_paging_token(result=data, order_by=sort_key, per_page=per_page)
        return data, next_token

    def get_list_popup_template_warehouse(
            self,
            merchant_id,
            account_id,
            search,
            is_favorite,
            branch_code,
            purpose_code,
            holiday_code,
            type_codes,
            sort_by,
            order_by,
            after_token,
            per_page
    ):
        option_oder = [(sort_by, order_by)]
        filter_option = {}
        if is_favorite:
            email_favorites = PopupTemplateFavoritesModel().get_popup_template_favorites_by_account_id(account_id,
                                                                                                       merchant_id)
            email_id_favorites = [ObjectId(email["email_id"]) for email in email_favorites]
            filter_option.update({"_id": {"$in": email_id_favorites}})

        if search:
            search = self._gen_data_build_search_query(search)
            filter_option.update({"title_unique": search})
        if branch_code:
            list_branch_code = branch_code.split(",")
            filter_option.update({"branch_codes": {"$in": list_branch_code}})
        if purpose_code:
            list_purpose_code = purpose_code.split(",")
            filter_option.update({"purpose_codes": {"$in": list_purpose_code}})
        if holiday_code:
            list_holiday_code = holiday_code.split(",")
            filter_option.update({"holiday_codes": {"$in": list_holiday_code}})
        if type_codes:
            list_type_codes = type_codes.split(",")
            filter_option.update({"type_codes": {"$in": list_type_codes}})
        data, after_token = self.find_paginate_load_more(
            filter_option, per_page, after_token, option_oder, projection={}
        )
        return data, after_token

    def get_popup_template_warehouse_by_title(self, title):
        return self.find_one({"title": title})

    def get_popup_template_warehouse_by_id(self, popup_template_id):
        return self.find_one({"_id": ObjectId(popup_template_id)})

    def update_popup_template_warehouse(self, popup_template_id, data):
        search_option = {"_id": ObjectId(popup_template_id)}
        return self.update_one_query(search_option, data)

    def delete_popup_template_warehouse(self, popup_template_id):
        search_option = {"_id": ObjectId(popup_template_id)}
        return self.delete_one(search_option)



