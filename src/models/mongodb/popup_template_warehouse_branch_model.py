#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from src.common import DatabaseName
from src.models.mongodb import BaseModel


class PopupTemplateWareHouseBranchModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_TEMPLATE_WAREHOUSE_BRANCH
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def get_list_branch(self):
        query = {}
        return self.find(query)

    def get_popup_template_warehouse_branch(self, branch_code):
        query = {"branch_code": branch_code}
        return self.find_one(query)

    def create_popup_warehouse_template_branch(self, branch):
        return self.insert(branch)
