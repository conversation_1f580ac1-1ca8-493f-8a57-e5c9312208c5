#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""

from src.common import DatabaseName
from src.models.mongodb import BaseModel


class PopupTemplateWareHouseTypesModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_TEMPLATE_WAREHOUSE_TYPES
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def get_list_types(self):
        query = {}
        return self.find(query)

    def get_popup_template_warehouse_types(self, types_code):
        query = {"type_code": types_code}
        return self.find_one(query)

    def create_popup_template_warehouse_types(self, types):
        return self.insert(types)