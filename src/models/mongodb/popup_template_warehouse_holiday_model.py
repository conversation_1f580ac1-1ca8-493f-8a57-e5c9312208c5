#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""

from src.common import DatabaseName
from src.models.mongodb import BaseModel


class PopupTemplateWareHouseHolidayModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_TEMPLATE_WAREHOUSE_HOLIDAY
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def get_list_holiday(self):
        query = {}
        return self.find(query)

    def get_popup_template_warehouse_holiday(self, holiday_code):
        query = {"holiday_code": holiday_code}
        return self.find_one(query)

    def create_popup_template_warehouse_holiday(self, holiday):
        return self.insert(holiday)