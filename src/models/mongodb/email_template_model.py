import math

from bson import ObjectId

from src.common import CommonParams, DatabaseName
from src.models.mongodb import BaseModel


class EmailTemplateModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.EMAIL_TEMPLATES

    def get_detail_email_template(self, template_id, merchant_id):
        search_option = {
            CommonParams.ID: ObjectId(template_id),
            CommonParams.MERCHANT_ID: merchant_id,
        }
        detail_email_template = self.find_one(search_option=search_option)
        return detail_email_template

    def delete_multi_email_template(self, merchant_id, lst_email_template_id):
        lst_email_template_id = [ObjectId(email_template_id) for email_template_id in lst_email_template_id]
        delete_options = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.ID: {
                "$in": lst_email_template_id
            }
        }
        return self.delete_many(delete_options)

    def delete_all_email_template(self):
        delete_options = {}
        return self.delete_many(delete_options)

    def count_all_email_template(self, search_options):
        count = self.count_documents(search_options)
        return count

    def get_list_email_template(self, merchant_id, type_email_template, paging):
        search_options = {}

        if merchant_id and type_email_template == CommonParams.PARTICULAR:
            search_options.update({
                CommonParams.MERCHANT_ID: merchant_id
            })

        if type_email_template:
            search_options.update({
                CommonParams.APPLIER_TYPE: type_email_template
            })

        if paging.get(CommonParams.PAGE) == -1:
            lst_email_template = list(self.find(search_options))
            total_count = self.count(search_options)
            total_page = 1
        else:
            limit_num, skip_num = EmailTemplateModel.generate_limit_num_and_skip_num(paging)
            lst_email_template = list(self.find(search_options).limit(limit_num).skip(skip_num))
            total_count = self.count(search_options)
            total_page = math.ceil(total_count / paging.get(CommonParams.PER_PAGE))

        results = {
            'lst_email_template': lst_email_template,
            CommonParams.TOTAL_PAGE: total_page,
            CommonParams.TOTAL_COUNT: total_count
        }
        return results
