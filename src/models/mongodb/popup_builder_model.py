import pymongo
from bson import ObjectId
from mobio.libs.logging import <PERSON><PERSON>Logging
from pymongo import IndexModel, ASCENDING, DESCENDING

from src.common import PopupBuilder, CommonParams, DatabaseName
from src.models.mongodb import BaseModel


class PopupBuilderModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_BUILDER
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def drop_index(self):
        try:
            self.conn_primary.drop_indexes()
            print('drop_index {} successfully'.format(self.collection_name))
        except Exception as er:
            err_msg = "drop_index Mongo, {}, ERR: {}".format(self.collection_name, er)
            print(err_msg)

    def sync_table(self):
        try:
            index_1 = IndexModel(
                [
                    (CommonParams.MERCHANT_ID, ASCENDING),
                    (CommonParams.UPDATED_TIME, DESCENDING),
                    (PopupBuilder.ACTIVE, DESCENDING),
                ],
                background=True,
                unique=False,
            )
            index_2 = IndexModel(
                [
                    (CommonParams.MERCHANT_ID, ASCENDING),
                    (PopupBuilder.TITLE_UNIQUE, ASCENDING)
                ],
                background=True,
                unique=False,
            )

            self.conn_primary.create_indexes([index_1, index_2])
            print(
                "popup_builder result: {}".format(
                    self.conn_primary.index_information()
                )
            )
        except Exception as er:
            err_msg = "sync_table Mongo, popup_builder, ERR: {}".format(er)
            print(err_msg)

    def get_detail_popup_builder(self, popup_builder_id, merchant_id):
        projection = {
            CommonParams.ID: 0,
            PopupBuilder.TITLE: 0,
            CommonParams.MERCHANT_ID: 0,
            CommonParams.SESSION: 0,
            PopupBuilder.ACTIVE: 0,
            PopupBuilder.THUMBNAIL: 0,
            PopupBuilder.SMALL_THUMBNAIL: 0,
            PopupBuilder.SECOND_PAGE: 0,
            PopupBuilder.CATEGORIES: 0,
            PopupBuilder.TYPE_CODES: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0
        }
        search_option = {
            CommonParams.ID: ObjectId(popup_builder_id),
            CommonParams.MERCHANT_ID: merchant_id,
        }
        detail_popup_builder = self.find_one(search_option, projection)
        return detail_popup_builder

    def get_detail_popup_builder_full_info(self, popup_builder_id, merchant_id):
        search_option = {
            CommonParams.ID: ObjectId(popup_builder_id),
            CommonParams.MERCHANT_ID: merchant_id,
        }
        detail_popup_builder = self.find_one(search_option)
        return detail_popup_builder

    def get_list_popup_builder(self, search_options, paging):
        sort_option = [(CommonParams.UPDATED_TIME, pymongo.DESCENDING)]
        projection = {
            CommonParams.AVATAR_LOCAL_PATH: 0,
            CommonParams.POPUP_LOCAL_PATH: 0,
            PopupBuilder.BODY_TEMPLATE: 0,
            PopupBuilder.BUILD_TEMPLATE: 0,
            PopupBuilder.JOURNEY_BUILDER_USING: 0,
            PopupBuilder.MASTER_CAMPAIGN_USING: 0,
            PopupBuilder.TITLE_CODE: 0,
            PopupBuilder.CONFIG_FIELD: 0,
            PopupBuilder.CURRENT_FIELD: 0,
            PopupBuilder.CONFIG_BUTTON: 0,
            PopupBuilder.CURRENT_BUTTON: 0,
            CommonParams.CREATED_TIME: 0,
            CommonParams.CREATED_BY: 0,
            CommonParams.UPDATED_TIME: 0,
            CommonParams.UPDATED_BY: 0
        }

        return self.get_list_entity(search_options, paging, projection, sort_option)

    @staticmethod
    def get_data_config_field(merchant_id, popup_builder_id):
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        data_config_field = []
        if detail_popup_builder:
            config_field = detail_popup_builder.get(PopupBuilder.CONFIG_FIELD, {})
            current_field = detail_popup_builder.get(PopupBuilder.CURRENT_FIELD, {})
            for key, value in config_field.items():
                is_available = PopupBuilder.AVAILABLE if key in current_field else PopupBuilder.NOT_AVAILABLE
                data_config_field.append({
                    PopupBuilder.FIELD_KEY: key,
                    PopupBuilder.FIELD_NAME: value,
                    PopupBuilder.IS_AVAILABLE: is_available
                })
        return data_config_field

    @staticmethod
    def get_data_config_button(merchant_id, popup_builder_id):
        detail_popup_builder = PopupBuilderModel().get_detail_popup_builder_full_info(popup_builder_id, merchant_id)
        config_button = detail_popup_builder.get(PopupBuilder.CONFIG_BUTTON, {})
        current_button = detail_popup_builder.get(PopupBuilder.CURRENT_BUTTON, {})
        data_config_button = []
        for key, value in config_button.items():
            is_available = PopupBuilder.AVAILABLE if key in current_button else PopupBuilder.NOT_AVAILABLE
            data_config_button.append({
                PopupBuilder.BUTTON_KEY: key,
                PopupBuilder.BUTTON_NAME: value,
                PopupBuilder.IS_AVAILABLE: is_available
            })
        return data_config_button

    def connect_with_many_popup_builder(self, list_bulk):
        result = self.conn_primary.bulk_write(list_bulk, ordered=False)
        MobioLogging().info(
            'connect_with_many_popup_builder - nModified: {}; nUpserted: {}; nMatched: {}; nInserted: {}'.format(
                result.bulk_api_result['nModified'],
                result.bulk_api_result['nUpserted'],
                result.bulk_api_result['nMatched'],
                result.bulk_api_result['nInserted'],
            )
        )
        result_update = {
            'nMatched': result.bulk_api_result['nMatched'],
            'nModified': result.bulk_api_result['nModified'],
            'nUpserted': result.bulk_api_result['nUpserted'],
            'nInserted': result.bulk_api_result['nInserted'],
        }
        return result_update

    # FOR JOURNEY BUILDER
    def get_list_id_popup_builder(self, search_options, paging):
        sort_option = [(CommonParams.UPDATED_TIME, pymongo.DESCENDING)]

        projection = {
            CommonParams.ID: 1
        }

        return self.get_list_entity(search_options, paging, projection, sort_option)

    def get_list_name_popup_builder(self, search_options, paging):
        sort_option = [(CommonParams.UPDATED_TIME, pymongo.DESCENDING)]
        projection = {
            CommonParams.ID: 1,
            PopupBuilder.TITLE: 1
        }

        return self.get_list_entity(search_options, paging, projection, sort_option)

    def get_list_name_popup_builder_by_list_id(self, merchant_id, lst_popup_builder_id):
        lst_popup_builder_id = [ObjectId(popup_builder_id) for popup_builder_id in lst_popup_builder_id]
        sort_option = [(CommonParams.UPDATED_TIME, pymongo.DESCENDING)]
        projection = {
            CommonParams.ID: 1,
            PopupBuilder.TITLE: 1
        }
        search_options = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.ID: {
                '$in': lst_popup_builder_id
            },
            '$and': [
                {
                    PopupBuilder.CURRENT_FIELD: {
                        '$exists': True
                    }
                },
                {
                    PopupBuilder.CURRENT_FIELD: {
                        '$ne': {}
                    }
                }
            ]
        }

        lst_name_popup_builder = list(self.select_all(search_options, projection).sort(sort_option))
        return lst_name_popup_builder

    def get_data_for_filter(self, merchant_id, popup_builder_id):
        projection = {
            PopupBuilder.JOURNEY_BUILDER_USING: 1,
            PopupBuilder.MASTER_CAMPAIGN_USING: 1
        }
        search_option = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.ID: ObjectId(popup_builder_id),
        }
        detail_popup_builder = self.find_one(search_option, projection)
        return detail_popup_builder

    def get_detail_popup_builder_by_title_unique(self, merchant_id, title_unique):
        search_options = {
            CommonParams.MERCHANT_ID: merchant_id,
            PopupBuilder.TITLE_UNIQUE: title_unique,
        }
        detail_popup_builder = self.find_one(search_options)
        return detail_popup_builder

    def update_unique_title(self, list_bulk):
        result = self.conn_primary.bulk_write(list_bulk, ordered=False)
        MobioLogging().info(
            'update_unique_title - nModified: {}; nUpserted: {}; nMatched: {}; nInserted: {}'.format(
                result.bulk_api_result['nModified'],
                result.bulk_api_result['nUpserted'],
                result.bulk_api_result['nMatched'],
                result.bulk_api_result['nInserted'],
            )
        )
        result_update = {
            'nMatched': result.bulk_api_result['nMatched'],
            'nModified': result.bulk_api_result['nModified'],
            'nUpserted': result.bulk_api_result['nUpserted'],
            'nInserted': result.bulk_api_result['nInserted'],
        }
        return result_update

    def support_workflow_get_detail_popup(self, popup_builder_id, merchant_id, select_field):
        projection = {
            CommonParams.ID: 0,
            PopupBuilder.TITLE: 1,
        }
        projection.update(
            **self.generate_projection_from_select_fields(select_field)
        )
        search_option = {
            CommonParams.ID: ObjectId(popup_builder_id),
            CommonParams.MERCHANT_ID: merchant_id,
        }
        detail_popup_builder = self.find_one(search_option, projection)
        return detail_popup_builder

    @classmethod
    def generate_projection_from_select_fields(cls, select_fields):
        projection = {}
        for select_field in select_fields:
            projection.update({
                select_field: 1
            })
        return projection


if __name__ == '__main__':
    search_option = {
        'merchant_id': '1b99bdcf-d582-4f49-9715-1b61dfff3924',
        'popup_builder_id': '62301d8dbfce426fcf3ff4df'
    }
    search_option['_id'] = ObjectId(search_option.pop('popup_builder_id'))
    select_field = []
    PopupBuilderModel().support_workflow_get_detail_popup(search_option=search_option, select_field=select_field)
