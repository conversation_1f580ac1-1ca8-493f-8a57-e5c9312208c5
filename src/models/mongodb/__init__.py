#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TruongCL
    Company: Mobio
    Date Created: 10/10/2021
"""
import math
import re
import uuid
import json
from bson.objectid import ObjectId
from pymongo import MongoClient

from configs import MongoConfig
from src.common import CommonParams
from src.common.utils import Base64,utf8_to_ascii
from datetime import datetime

base_client = MongoClient(MongoConfig.MONGO_URI)
db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", MongoConfig.MONGO_URI).group(1)


class BaseModel:
    client = base_client
    collection_name = ''
    db_client = client.get_database(db_name)

    def _db(self):
        return self.client[db_name]

    def get_db(self):
        db = self.client[db_name]
        collection = db[self.collection_name]
        return collection

    def insert(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def insert_many(self, document):
        db = self.client[db_name]
        return db[self.collection_name].insert_many(document)

    def insert_document(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    def update_set_dictionary(self, search_option, dictionary):
        db = self.client[db_name]

        document = db[self.collection_name].find_one(search_option)
        if document:
            return db[self.collection_name].update_one(filter=search_option, update={'$set': dictionary},
                                                       upsert=True).matched_count >= 1
        return None

    def update_dictionary(self, document_id, dictionary):
        if isinstance(document_id, str):
            document_id = ObjectId(document_id)
        return self.collector().update_one({"_id": document_id}, dictionary).matched_count

    def update_one_query(self, query, data):
        return self.client.get_database(db_name)[self.collection_name].update_one(query,
                                                                                  {"$set": data}).matched_count

    def update_many(self, filter_option, update_option):
        db = self.client[db_name]
        db[self.collection_name].update_many(filter_option, update_option)

    def update(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        db[self.collection_name].update(filter_option, update_option, upsert=upsert, multi=multi)

    def update_by_set(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        return db[self.collection_name].update(filter_option, {"$set": update_option}, upsert=upsert, multi=multi)

    def delete_one(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_one(delete_options)

    def delete_many(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_many(delete_options).deleted_count

    def upsert(self, search_option, dictionary):
        db = self.client[db_name]
        out = True
        document = db[self.collection_name].find_one(search_option)
        if document:
            document.update(dictionary)
            self.collector().replace_one({"_id": document.get('_id')}, dictionary, upsert=True)
            out = False
        else:
            db[self.collection_name].insert_one(dictionary)
        return out

    def upsert_document(self, condition, dictionary):
        return self.collector().replace_one(condition, dictionary, upsert=True).matched_count

    def find(self, search_option, obj_field_select: dict = None):
        db = self.client[db_name]
        if obj_field_select:
            return db[self.collection_name].find(search_option, obj_field_select)
        return db[self.collection_name].find(search_option)

    def find_one(self, search_option, projection=None, sort=None):
        return self.collector().find_one(search_option, projection, sort=sort)

    def collector(self):
        return self._db()[self.collection_name]

    def count_by_query(self, count_option):
        db = self.client[db_name]
        return db[self.collection_name].count_documents(count_option)

    def count(self, search_option=None):
        if not search_option:
            search_option = {}
        return self.collector().count_documents(search_option)

    def select_all(self, search_option, projection=None):
        return self.collector().find(search_option, projection)

    def find_paginate(self, search_option, page=0, per_page=None, sort_option=None, projection=None):
        collection = self.collector().find(search_option, projection)
        if sort_option:
            collection = collection.sort(sort_option)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    def get_list_entity(self, search_options, paging, projection=None, sort_option=None):
        cursor = self.select_all(search_options, projection)
        if sort_option:
            cursor.sort(sort_option)

        total_page = 1
        total_count = self.count(search_options)

        if paging.get(CommonParams.PAGE) != -1:
            limit_num, skip_num = self.generate_limit_num_and_skip_num(paging)
            cursor.limit(limit_num).skip(skip_num)
            total_page = math.ceil(total_count / paging.get(CommonParams.PER_PAGE))

        list_data = list(cursor)
        results = {
            CommonParams.LIST_DATA: list_data,
            CommonParams.TOTAL_PAGE: total_page,
            CommonParams.TOTAL_COUNT: total_count
        }
        return results

    def _aggregate(self, group, match: object, sort=None, project=None):
        db = self.client[db_name]
        pipeline = []
        if match:
            pipeline.append({"$match": match})
        pipeline.append({"$group": group})
        if sort:
            pipeline.append({"$sort": sort})
        if project:
            pipeline.append({"$project": project})
        return db[self.collection_name].aggregate(pipeline)

    def distinct(self, fields, query):
        db = self.client[db_name]

        if type(fields) is str:
            return db[self.collection_name].distinct(fields, query)

        return None

    @staticmethod
    def normalize_uuid(some_uuid):
        if isinstance(some_uuid, str):
            return uuid.UUID(some_uuid)
        return some_uuid

    @staticmethod
    def normalize_object_id(some_object_id):
        if isinstance(some_object_id, str):
            return ObjectId(some_object_id)
        return some_object_id

    @staticmethod
    def generate_limit_num_and_skip_num(paging):
        limit_num = paging.get(CommonParams.PER_PAGE)
        skip_num = (int(paging.get(CommonParams.PAGE)) - 1) * int(paging.get(CommonParams.PER_PAGE))
        return limit_num, skip_num

    @staticmethod
    def serialize_id(data: dict):
        if '_id' in data:
            value = data.pop('_id')
            data['id'] = str(value)

    def _gen_data_build_search_query(self, search_keywords):
        if search_keywords:
            normalize_search = utf8_to_ascii(search_keywords)
            normalize_search = normalize_search.lower()
            import re

            rgx = re.compile(".*{}.*".format(normalize_search), re.IGNORECASE)
            return rgx
        return None

    @staticmethod
    def generate_paging_token(result, order_by, per_page):
        paging_token = None
        order_by = order_by.split(".")[-1]
        if result and len(result) == per_page:
            # Generate token
            row = result[-1]
            token = {
                "last_id": str(row.get("_id")) if "_id" in row else row["id"],
                "field": {"key": order_by, "value": row.get(order_by)},
            }
            if isinstance(token["field"]["value"], datetime):
                token["field"]["value"] = token["field"]["value"].timestamp()
            json_token = json.dumps(token)
            paging_token = Base64.encode(json_token)
        return paging_token

    @classmethod
    def parse_token(cls, token):
        if not token:
            return None
        condition = json.loads(Base64.decode(token))
        return condition