import math
from bson import ObjectId
from pymongo import IndexModel, ASCENDING, DESCENDING

from src.common import CommonParams, DatabaseName, EmailTemplate
from src.models.mongodb import BaseModel
from src.common import DatetimeTypeKeys
from datetime import datetime
from mobio.libs.logging import MobioLogging

from src.models.mongodb.email_template_favorites_model import EmailTemplateFavoritesModel


class EmailBuilderModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.EMAIL_BUILDER
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def sync_table(self):
        try:
            # self.conn_primary.drop_indexes()
            index_1 = IndexModel([
                (CommonParams.MERCHANT_ID, ASCENDING),
                (EmailTemplate.NAME, ASCENDING),
            ],
                background=True,
                unique=True,
            )
            index_2 = IndexModel([
                (CommonParams.MERCHANT_ID, ASCENDING),
                (EmailTemplate.CATEGORIES, ASCENDING),
            ],
                background=True,
                unique=False,
            )
            index_3 = IndexModel([
                (CommonParams.MERCHANT_ID, ASCENDING),
                (EmailTemplate.NAME_UNSIGNED, ASCENDING),
            ],
                background=True,
                unique=False,
            )

            self.conn_primary.create_indexes([index_1, index_2, index_3])
            print(
                "{} result: {}".format(
                    self.collection_name, self.conn_primary.index_information()
                )
            )
        except Exception as er:
            err_msg = "sync_table Mongo, ColorModel, ERR: {}".format(er)
            print(err_msg)

    def get_email_builder_by_name(self, name):
        return self.find_one({"name": name})

    def find_paginate_load_more(
        self,
        search_option={},
        per_page=20,
        after_token=None,
        sort_option=[("_id", -1)],
        projection=None,
        list_field_datetime=DatetimeTypeKeys.VALUES,
    ):
        """
        Support sort field type: number, _id
        :param search_option:
        :param per_page:
        :param after_token:
        :param sort_option: (chỉ support sort 1 field)
        :param projection: (Bao gồm field được sort)
        :return:
        """
        sort_key = sort_option[0][0]
        sort_value = sort_option[0][1]
        sort_filter = {}
        # Check token
        if after_token:
            operator_query = "$lt" if sort_value == -1 else "$gt"
            token = self.parse_token(after_token)
            field = token.get("field")
            last_id = token.get("last_id")
            if "key" in field and "value" in field:
                key_sorted = field["key"]
                value_sorted = field["value"]
                if key_sorted in list_field_datetime:
                    value_sorted = datetime.fromtimestamp(float(value_sorted))

                try:
                    last_id = ObjectId(last_id)
                except:
                    last_id = int(last_id)

                item_sort_filter = [
                    {key_sorted: {operator_query: value_sorted}},
                    {key_sorted: value_sorted, "_id": {operator_query: last_id}},
                ]

                if "$or" in search_option:
                    sort_filter = {
                        "$or": [
                            {"$and": [cond1, cond2]} for cond1 in search_option["$or"] for cond2 in item_sort_filter
                        ]
                    }
                else:
                    sort_filter = {"$or": item_sort_filter}
        find_filter = {**search_option, **sort_filter}
        MobioLogging().info("find_paginate_load_more :: find_filter :: {}".format(find_filter))
        collection = self.find(find_filter, projection).limit(per_page)
        sort_option.append(("_id", sort_value))
        collection = collection.sort(sort_option)
        data = [x for x in collection]
        next_token = BaseModel.generate_paging_token(result=data, order_by=sort_key, per_page=per_page)
        return data, next_token

    def get_detail_email_builder(self, template_id, merchant_id):
        search_option = {
            CommonParams.ID: ObjectId(template_id),
            CommonParams.MERCHANT_ID: merchant_id,
        }
        detail_email_builder = self.find_one(search_option=search_option)
        return detail_email_builder

    def delete_multi_email_builder(self, merchant_id, lst_email_builder_id):
        delete_options = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.ID: {
                "$in": lst_email_builder_id
            }
        }
        return self.delete_many(delete_options)


    def count_all_email_builder(self, search_options):
        count = self.count_by_query(search_options)
        return count

    def get_list_email_builder(self, merchant_id, type_email_builder, paging):
        search_options = {}

        if merchant_id and type_email_builder == CommonParams.PARTICULAR:
            search_options.update({
                CommonParams.MERCHANT_ID: merchant_id
            })

        if type_email_builder:
            search_options.update({
                CommonParams.APPLIER_TYPE: type_email_builder
            })

        if paging.get(CommonParams.PAGE) == -1:
            lst_email_builder = list(self.find(search_options))
            total_count = self.count(search_options)
            total_page = 1
        else:
            limit_num, skip_num = EmailBuilderModel.generate_limit_num_and_skip_num(paging)
            lst_email_builder = list(self.find(search_options).limit(limit_num).skip(skip_num))
            total_count = self.count(search_options)
            total_page = math.ceil(total_count / paging.get(CommonParams.PER_PAGE))

        results = {
            'lst_email_template': lst_email_builder,
            CommonParams.TOTAL_PAGE: total_page,
            CommonParams.TOTAL_COUNT: total_count
        }
        return results


    def get_list_email_builder_by_category(self, merchant_id, category_ids):
        search_options = {
            CommonParams.MERCHANT_ID: merchant_id,
            EmailTemplate.CATEGORIES: {
                "$in": category_ids
            }
        }
        return self.find(search_options)

    def get_emails_builder_by_condition(
            self,
            merchant_id,
            account_id,
            is_favorite,
            status,
            search,
            categories,
            sort_by,
            order_by,
            per_page,
            after_token):

        option_order = [(sort_by, order_by)]
        search_options = {
            CommonParams.MERCHANT_ID: merchant_id,
        }
        if status:
            search_options.update({
                EmailTemplate.STATUS: int(status)
            })
        if search:
            search = self._gen_data_build_search_query(search)
            search_options.update({
                EmailTemplate.NAME_UNSIGNED: {
                    "$regex": search
                }
            })
        # TH categories = "" => lấy ra email template có danh mục là mặc định
        # TH categories = None => không lọc theo danh mục
        if categories is not None:
            lst_categories = [cat.strip() for cat in categories.split(",") if cat.strip()]
            if lst_categories:
                search_options.update({
                    EmailTemplate.CATEGORIES: {
                        "$in": lst_categories
                    }
                })
            else:
                search_options.update({
                    EmailTemplate.CATEGORIES: {'$size': 0 }
                })
        if is_favorite:
            email_favorites = EmailTemplateFavoritesModel().get_email_template_favorites_by_account_id(account_id, merchant_id)
            email_id_favorites = [ObjectId(email["email_id"]) for email in email_favorites]
            search_options.update({"_id": {"$in": email_id_favorites}})

        data, after_token = self.find_paginate_load_more(
            search_options, per_page, after_token, option_order, {}
        )
        return data, after_token

    def get_list_email_builder_by_ids(self, merchant_id, ids):
        search_option = {
            CommonParams.MERCHANT_ID: merchant_id,
            CommonParams.ID: {
                "$in": ids
            }
        }
        lst_email_builder = self.find(search_option=search_option)
        return lst_email_builder
