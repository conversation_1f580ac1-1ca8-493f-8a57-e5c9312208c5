#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from src.common import DatabaseName
from src.models.mongodb import BaseModel
from pymongo import ASCENDING, IndexModel


class PopupTemplateFavoritesModel(BaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = DatabaseName.POPUP_TEMPLATE_FAVORITE
        self.conn_primary = self.db_client.get_collection(self.collection_name)

    def sync_table(self):
        try:
            # self.conn_primary.drop_indexes()
            index_1 = IndexModel(
                [
                    ("account_id", ASCENDING),
                    ("merchant_id", ASCENDING),
                    ("popup_template_id", ASCENDING),
                ],
                background=True,
                unique=True,
            )

            self.conn_primary.create_indexes([index_1])
            print(
                "{} result: {}".format(
                    self.collection_name, self.conn_primary.index_information()
                )
            )
        except Exception as er:
            err_msg = "sync_table Mongo, ColorModel, ERR: {}".format(er)
            print(err_msg)

    def get_popup_template_favorites_by_account_id(self, account_id, merchant_id):
        return self.find({"account_id": account_id, "merchant_id": merchant_id})

    def update_popup_template_favorite(self, account_id, merchant_id, popup_template_id, is_favorite):
        if is_favorite:
            existing_favorite_db = self.find_one(
                {"account_id": account_id, "merchant_id": merchant_id, "popup_template_id": popup_template_id})
            if existing_favorite_db:
                return existing_favorite_db
            return self.insert({"account_id": account_id, "merchant_id": merchant_id, "popup_template_id": popup_template_id})
        else:
            return self.delete_one({"account_id": account_id, "merchant_id": merchant_id, "popup_template_id": popup_template_id})

    def get_popup_template_favorite_by_popup_template_id(self, account_id, merchant_id, popup_template_id):
        return self.find_one({
            "account_id": account_id,
            "merchant_id": merchant_id,
            "popup_template_id": popup_template_id
        })

