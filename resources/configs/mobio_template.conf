[logging_mode]
write_traceback_for_all_customize_exception = 1
write_traceback_for_global_exception = 1
log_for_request_success = 1
log_for_all_customize_exception = 1
log_for_global_exception = 1
file_max_bytes=0
file_backup_count=0

[lang]
keys = vi, en
default = vi

[third_party_service]
adm_config={host}/adm/v1.0/merchants/{merchant_id}/configs

# Admin
get_name_master_campaign={host}/adm/api/v2.1/master-campaigns/{master_campaign_id}

# Journey builder
get_node_name_by_id={host}api/v1.0/journey/nodes
check_master_campaign_is_in_use={host}/journey/api/v1.0/journeys/common?lang=vi&page=0&per_page=1
