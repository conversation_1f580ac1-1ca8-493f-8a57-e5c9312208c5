{"bad_request": {"message": "Bad request.", "code": 400}, "unauthorized": {"message": "JWT is invalid or is expired. Please login again.", "code": 401}, "not_allowed": {"message": "You can't execute, please login before execution this api.", "code": 405}, "not_found": {"message": "Item is not found.", "code": 404}, "internal_server_error": {"message": "There is an internal server error.", "code": 500}, "validate_error": {"message": "Validation failed.", "code": 412}, "lang_error": {"message": "System only support for language (vi,en).", "code": 100}, "must_not_empty": {"message": "%s must not empty.", "code": 101}, "not_exist": {"message": "%s [%s] not exist in system.", "code": 102}, "already_exist": {"message": "%s [%s] already exist in system.", "code": 103}, "message_success": {"message": "Request successful.", "code": 200}, "validate_format_object_id": {"message": "Not the format of ID", "code": 412}, "add_email_template_error": {"message": "Add email template error", "code": 412}, "create_img_error": {"message": "Create image error.", "code": 412}, "update_link_image_error": {"message": "Update link image error", "code": 412}, "email_template_not_exist": {"message": "Email template not exist.", "code": 102}, "data_update_not_exist": {"message": "Data update not exist.", "code": 102}, "delete_email_template_error": {"message": "Delete email template error.", "code": 412}, "type_email_template_not_exist": {"message": "Type email template not exist.", "code": 102}, "add_popup_template_error": {"message": "Add popup template error", "code": 412}, "popup_template_not_exist": {"message": "Popup template not exist.", "code": 102}, "delete_popup_template_error": {"message": "Delete popup template error.", "code": 412}, "type_popup_template_not_exist": {"message": "Type popup template not exist.", "code": 102}, "name_popup_template_already_exists_in_system": {"message": "Name popup template already exists in system.", "code": 102}, "session_dissimilarity": {"message": "Session gone", "code": 410}, "add_popup_builder_error": {"message": "Add popup builder error", "code": 412}, "copy_popup_builder_error": {"message": "Copy popup builder error", "code": 412}, "popup_builder_not_exist": {"message": "Popup builder not exist.", "code": 102}, "popup_builder_not_active": {"message": "Popup builder not active.", "code": 102}, "name_popup_builder_already_exists_in_system": {"message": "Name popup builder already exists in system.", "code": 102}, "add_category_error": {"message": "Add category error", "code": 412}, "delete_category_error": {"message": "Delete category error.", "code": 412}, "category_not_exist": {"message": "Category not exist.", "code": 102}, "add_popup_draft_error": {"message": "Add popup draft error", "code": 412}, "popup_draft_not_exist": {"message": "Popup draft not exist.", "code": 102}, "delete_popup_builder_error": {"message": "Delete popup builder error.", "code": 412}, "popup_is_being_used": {"message": "Popup is being used.", "code": 412}, "publish_popup_builder_error": {"message": "Publish popup builder error.", "code": 412}, "popup_builder_is_latest": {"message": "Popup builder is latest.", "code": 412}, "update_popup_draft_id_to_popup_builder_error": {"message": "Update identification of popup draft to popup builder error.", "code": 412}, "delete_popup_draft_error": {"message": "Delete popup draft error.", "code": 412}, "add_buttons_template_error": {"message": "Add button template error", "code": 412}, "buttons_template_not_exist": {"message": "Button template not exist.", "code": 102}, "delete_buttons_template_error": {"message": "Delete button template error.", "code": 412}, "type_buttons_template_not_exist": {"message": "Type button template not exist.", "code": 102}, "add_counters_template_error": {"message": "Add counter template error", "code": 412}, "counters_template_not_exist": {"message": "Counter template not exist.", "code": 102}, "delete_counters_template_error": {"message": "Delete counter template error.", "code": 412}, "type_counters_template_not_exist": {"message": "Type counter template not exist.", "code": 102}, "add_forms_template_error": {"message": "Add form template error", "code": 412}, "forms_template_not_exist": {"message": "Form template not exist.", "code": 102}, "delete_forms_template_error": {"message": "Delete form template error.", "code": 412}, "type_forms_template_not_exist": {"message": "Type form template not exist.", "code": 102}, "add_icons_template_error": {"message": "Add icon template error", "code": 412}, "icons_template_not_exist": {"message": "Icon template not exist.", "code": 102}, "delete_icons_template_error": {"message": "Delete icon template error.", "code": 412}, "type_icons_template_not_exist": {"message": "Type icon template not exist.", "code": 102}, "add_font_template_error": {"message": "Add font template error", "code": 412}, "font_template_not_exist": {"message": "Font template not exist.", "code": 102}, "delete_font_template_error": {"message": "Delete font template error.", "code": 412}, "name_already_exists": {"message": "Font name already exists in system.", "code": 103}, "add_font_default_error": {"message": "Add font default error.", "code": 412}, "delete_font_default_error": {"message": "Delete font default error.", "code": 412}, "font_default_not_exist": {"message": "Font default not exists.", "code": 412}, "must_have_font": {"message": "Must have font.", "code": 412}, "name_already_exists_in_font_default": {"message": "Name already exists in Google font.", "code": 412}, "i18n_register": {"message": "Register"}, "i18n_notifications": {"message": "Notifications"}, "i18n_promotion_offer": {"message": "Promotion & Offer"}, "i18n_contact": {"message": "Contact"}, "i18n_appreciation_gifts": {"message": "Appreciation Gifts"}, "i18n_others": {"message": "Others"}, "cannot_create_site": "Không tạo được site. <PERSON><PERSON> lòng thử lại sau.", "site_duplicate_name": "Name is exists. Please choose other name", "have_field_not_support": {"message": "Have field not support.", "code": 412}, "license_not_allowed": {"message": "To register for the function, please contact %s.", "code": 402}, "license_effectiveness_applied_to_each_channel": {"message": "The functionality is limited according to the business service package. To register for the function, please contact %s.", "code": 402}}