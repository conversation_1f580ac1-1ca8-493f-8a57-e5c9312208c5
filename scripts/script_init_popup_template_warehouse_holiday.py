#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from src.models.mongodb.popup_template_warehouse_holiday_model import PopupTemplateWareHouseHolidayModel
import datetime

list_holiday = [
    {
        "holiday_code": "birthday",
        "holiday_name": {
            "en": "Birthday",
            "vi": "Sinh nhật"
        }
    },
    {
        "holiday_code": "black_friday",
        "holiday_name": {
            "en": "Black Friday",
            "vi": "Black Friday"
        }
    },
    {
        "holiday_code": "new_year",
        "holiday_name": {
            "en": "New Year",
            "vi": "Tết dương lịch"
        }
    },
    {
        "holiday_code": "lunar_new_year",
        "holiday_name": {
            "en": "Lunar New Year",
            "vi": "Tết âm lịch"
        }
    },
    {
        "holiday_code": "christmas",
        "holiday_name": {
            "en": "Christmas",
            "vi": "<PERSON><PERSON><PERSON><PERSON> sin<PERSON>"
        }
    },
    {
        "holiday_code": "valentines_day",
        "holiday_name": {
            "en": "Valentine's Day",
            "vi": "<PERSON><PERSON> tình nhân 14/2"
        }
    },
    {
        "holiday_code": "international_womens_day",
        "holiday_name": {
            "en": "International Women's Day",
            "vi": "Quốc tế Phụ nữ 8/3"
        }
    },
    {
        "holiday_code": "labour_day",
        "holiday_name": {
            "en": "Labour Day",
            "vi": "Quốc tế Lao động 1/5"
        }
    },
    {
        "holiday_code": "national_day",
        "holiday_name": {
            "en": "National Day",
            "vi": "Quốc khánh"
        }
    },
    {
        "holiday_code": "back_to_school",
        "holiday_name": {
            "en": "Back to School",
            "vi": "Khai giảng"
        }
    },
    {
        "holiday_code": "vietnamese_womens_day",
        "holiday_name": {
            "en": "Vietnamese Women's Day",
            "vi": "Phụ nữ Việt Nam 20/10"
        }
    },
    {
        "holiday_code": "halloween",
        "holiday_name": {
            "en": "Halloween",
            "vi": "Halloween"
        }
    }
]

if __name__ == "__main__":
    current_time = datetime.datetime.now(datetime.timezone.utc)
    for holiday in list_holiday:
        db_holiday = PopupTemplateWareHouseHolidayModel().get_popup_template_warehouse_holiday(holiday["holiday_code"])
        if not db_holiday:
            holiday["created_time"] = current_time
            holiday["updated_time"] = current_time
            holiday["created_by"] = "admin"
            holiday["updated_by"] = "admin"
            PopupTemplateWareHouseHolidayModel().create_popup_template_warehouse_holiday(holiday)