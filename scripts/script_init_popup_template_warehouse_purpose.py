#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
import datetime
from src.models.mongodb.popup_template_warehouse_purpose_model import PopupTemplateWareHousePurposeModel


list_purpose = [
    {
        "purpose_code": "data_collection",
        "purpose_name": {
            "vi": "Thu thập thông tin",
            "en": "Information collection"
        }
    },
    {
        "purpose_code": "sales_boost",
        "purpose_name": {
            "vi": "Tăng doanh số",
            "en": "Sales boost"
        }
    },
    {
        "purpose_code": "promotion" ,
        "purpose_name": {
            "vi": "Khuyến mãi",
            "en": "Promotion"
        }
    },
    {
        "purpose_code": "advertising",
        "purpose_name": {
            "vi": "Qu<PERSON>ng cáo, giới thiệu sản phẩm",
            "en": "Advertising and Product Introduction"
        }
    },
    {
        "purpose_code": "call_registration",
        "purpose_name": {
            "vi": "<PERSON><PERSON>ng ký nhận cuộc gọ<PERSON>",
            "en": "Call registration"
        }
    },
    {
        "purpose_code": "feedback_collection",
        "purpose_name": {
            "vi": "Thu thập phản hồi",
            "en": "Feedback collection"
        }
    },
    {
        "purpose_code": "customer_engagement",
        "purpose_name": {
            "vi": "Duy trì kết nối",
            "en": "Customer Engagement"
        }
    },
    {
        "purpose_code": "special_occasion",
        "purpose_name": {
            "vi": "Đánh dấu dịp",
            "en": "Celebrate Special Moments"
        }
    }
]

if __name__ == "__main__":
    current_time = datetime.datetime.now(datetime.timezone.utc)
    for purpose in list_purpose:
        db_purpose = PopupTemplateWareHousePurposeModel().get_popup_template_warehouse_purpose(purpose["purpose_code"])
        if not db_purpose:
            purpose["created_time"] = current_time
            purpose["updated_time"] = current_time
            purpose["created_by"] = "admin"
            purpose["updated_by"] = "admin"
            PopupTemplateWareHousePurposeModel().create_popup_template_warehouse_purpose(purpose)