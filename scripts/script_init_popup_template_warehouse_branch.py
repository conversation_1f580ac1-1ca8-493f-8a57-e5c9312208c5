#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from src.models.mongodb.popup_template_warehouse_branch_model import PopupTemplateWareHouseBranchModel
import datetime

list_branches = [
    {
        "branch_code": "business_and_finance",
        "branch_name": {
            "vi": "Kinh doanh & tài ch<PERSON>h",
            "en": "Business & Finance"
        }
    },
    {
        "branch_code": "insurance",
        "branch_name": {
            "vi": "Bảo hiểm",
            "en": "Insurance"
        }
    },
    {
        "branch_code": "real_estate",
        "branch_name": {
            "vi": "Bất động sản",
            "en": "Real Estate"
        }
    },
    {
        "branch_code": "e_commerce_and_retail",
        "branch_name": {
            "vi": "Thương mại điện tử & bán lẻ",
            "en": "E-commerce & Retail"
        }
    },
    {
        "branch_code": "education",
        "branch_name": {
            "vi": "<PERSON><PERSON><PERSON><PERSON> dụ<PERSON>",
            "en": "Education"
        }
    },
    {   "branch_code": "healthcare_and_beauty",
        "branch_name": {
            "vi": "<PERSON>ăm sóc sức khỏe & sắc đẹp",
            "en": "Healthcare & Beauty"
        }
    },
    {
        "branch_code": "fashion",
        "branch_name": {
            "vi": "Thời trang",
            "en": "Fashion"
        }
    },
    {
        "branch_code": "entertainment_and_arts",
        "branch_name": {
            "vi": "Giải trí & nghệ thuật",
            "en": "Entertainment & Arts"
        }
    },
    {
        "branch_code": "hotels_and_tourism",
        "branch_name": {
            "vi": "Khách sạn & du lịch",
            "en": "Hotels & Tourism"
        }
    },
    {
        "branch_code": "restaurants",
        "branch_name": {
            "vi": "Nhà hàng",
            "en": "Restaurants"
        }
    },
    {
        "branch_code": "food",
        "branch_name": {
            "vi": "Thực phẩm",
            "en": "Food"
        }
    },
    {
        "branch_code": "agriculture",
        "branch_name": {
            "vi": "Nông nghiệp",
            "en": "Agriculture"
        }
    },
    {
        "branch_code": "information_technology",
        "branch_name": {
            "vi": "Công nghệ thông tin",
            "en": "Information Technology"
        }
    },
    {
        "branch_code": "architecture_and_interior_design",
        "branch_name": {
            "vi": "Kiến trúc, nội thất",
            "en": "Architecture & Interior Design"
        }
    },
    {
        "branch_code": "sport",
        "branch_name": {
            "vi": "Thể thao",
            "en": "Sport"
        }
    }
]

if __name__ == "__main__":
    current_time = datetime.datetime.now(datetime.timezone.utc)
    for branch in list_branches:
        db_branch = PopupTemplateWareHouseBranchModel().get_popup_template_warehouse_branch(branch["branch_code"])
        if not db_branch:
            branch["created_time"] = current_time
            branch["updated_time"] = current_time
            branch["created_by"] = "admin"
            branch["updated_by"] = "admin"
            PopupTemplateWareHouseBranchModel().create_popup_warehouse_template_branch(branch)