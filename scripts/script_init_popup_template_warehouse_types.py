#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
from datetime import datetime
from src.models.mongodb.popup_template_warehouse_type_model import PopupTemplateWareHouseTypesModel


list_type = [
    {
        "purpose_code": "announcement",
        "purpose_name": {
            "vi": "Thông báo",
            "en": "Announcement"
        }
    },
    {
        "purpose_code": "news",
        "purpose_name": {
            "vi": "Tin tức",
            "en": "News"
        }
    },
    {
        "purpose_code": "event_invitation" ,
        "purpose_name": {
            "vi": "Thư mời tới sự kiện",
            "en": "Event invitation"
        }
    },
    {
        "purpose_code": "advertisement",
        "purpose_name": {
            "vi": "Quảng cáo",
            "en": "Advertisement"
        }
    },
    {
        "purpose_code": "welcome",
        "purpose_name": {
            "vi": "Chào mừng",
            "en": "Welcome"
        }
    },
    {
        "purpose_code": "thank_you",
        "purpose_name": {
            "vi": "Cảm ơn",
            "en": "Thank you"
        }
    },
    {
        "purpose_code": "congratulation",
        "purpose_name": {
            "vi": "Chúc mừng",
            "en": "Congratulation"
        }
    }
]

if __name__ == "__main__":
    for type in list_type:
        db_purpose = PopupTemplateWareHouseTypesModel().get_popup_template_warehouse_types(type["type_code"])
        if not db_purpose:
            type["created_time"] = datetime.now()
            type["updated_time"] = datetime.now()
            type["created_by"] = None
            type["updated_by"] = None
            PopupTemplateWareHouseTypesModel().create_popup_template_warehouse_types(type)