#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 05/06/2025
"""
import datetime
from src.models.mongodb.popup_template_warehouse_type_model import PopupTemplateWareHouseTypesModel


list_type = [
    {
        "type_code": "lightbox",
        "type_name": {
            "vi": "Lightbox",
            "en": "Lightbox"
        }
    },
    {
        "type_code": "fullscreen",
        "type_name": {
            "vi": "Toàn màn hình",
            "en": "Fullscreen"
        }
    },
    {
        "type_code": "floating_bar",
        "type_name": {
            "vi": "Floating bar",
            "en": "Floating bar"
        }
    }
]

if __name__ == "__main__":
    current_time = datetime.datetime.now(datetime.timezone.utc)
    for type in list_type:
        db_types = PopupTemplateWareHouseTypesModel().get_popup_template_warehouse_types(type["type_code"])
        print('=================CHECK DEN DAY RUI NHE =================\n')
        print(db_types)
        print('=================CHECK DEN DAY RUI NHE =================\n')
        if not db_types:
            type["created_time"] = current_time
            type["updated_time"] = current_time
            type["created_by"] = "admin"
            type["updated_by"] = "admin"
            PopupTemplateWareHouseTypesModel().create_popup_template_warehouse_types(type)