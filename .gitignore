# Byte-compiled / optimized / DLL files

__pycache__/
*.py[cod]
*$py.class
.idea/
# C extensions
*.so
*.c

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
static/export_excel
static/import_deal
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
.static_storage/
.media/
local_settings.py


# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
tungdd.env
local.env
envinroments.env
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.DS_Store
save_img/*

configs/local.py
configs/dev.py
configs/test.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# visual studio code
.vscode/

logs
monitor_logs/

#resources/configs/

tmp/
static/

info_popup_template.txt
info_objects_template.txt
info_categories.txt
*.env
truongcl.env