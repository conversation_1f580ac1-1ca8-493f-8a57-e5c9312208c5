# -*- coding: utf-8 -*-
from src.models.mongodb.color_model import ColorModel
from src.models.mongodb.export_report.information_for_report_model import (
    InformationForReportModel,
)
from src.models.mongodb.export_report.information_history_model import (
    InformationHistoryModel,
)
from src.models.mongodb.export_report.information_profile_model import (
    InformationProfileModel,
)
from src.models.mongodb.export_report.summary_for_report_model import (
    SummaryForReportModel,
)
from src.models.mongodb.font_default_model import FontDefaultModel
from src.models.mongodb.font_template_model import FontTemplateModel
from src.models.mongodb.objects_template_model import ObjectsTemplateModel
from src.models.mongodb.popup_builder_model import PopupBuilderModel
from src.models.mongodb.popup_template_model import PopupTemplateModel

if __name__ == "__main__":
    FontDefaultModel().sync_table()
    FontTemplateModel().sync_table()
    ObjectsTemplateModel().sync_table()
    PopupBuilderModel().sync_table()
    PopupTemplateModel().sync_table()
    SummaryForReportModel().sync_table()
    InformationForReportModel().sync_table()
    InformationHistoryModel().sync_table()
    InformationProfileModel().sync_table()
    ColorModel().sync_table()
