#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: TruongCL
    Company: MobioVN
    Date created: 25/04/2021
"""


class ConsumerGroup:
    MOBIO_TEMPLATE_CONSUMER_FOR_REPORT = "mobio-template-consumer-for-report"
    MOBIO_TEMPLATE_GROUP_SUPPORT_WORKFLOW_GET_INFO = "mobio-template-group-support-workflow-get-info"


class KafkaTopic:
    DIGIENTY_POPUP_BUILDER_USER_BEHAVIOUR_TRACKED_EVENT = "digienty-popup-builder-user-behaviour-tracked-event"
    E72_TAGS_INTERACTIVE = 'e72-tags-interactive'
    UPSERT_PROFILE_WITH_CALLBACK = 'upsert-profile-with-callback'
    SUPPORT_WORKFLOW_GET_INFO = 'mobio-template-support-workflow-get-info'
    PROFILING_DEVICE_CONVERT_TO_PROFILE = 'profiling-device-convert-to-profile'

    LIST_TOPIC = [
        DIGIENTY_POPUP_BUILDER_USER_BEHAVIOUR_TRACKED_EVENT,
        E72_TAGS_INTERACTIVE,
        UPSERT_PROFILE_WITH_CALLBACK,
        SUPPORT_WORKFLOW_GET_INFO,
        PROFILING_DEVICE_CONVERT_TO_PROFILE
    ]
