import os
import sys

from configs.kafka_config import ConsumerGroup, KafkaTopic
from src.models.mongodb import base_client

# from mobio.libs.kafka_lib.helpers.confulent_consumer_manager import ConfluentConsumerManager


if __name__ == "__main__":
    name_queue = sys.argv[1]
    number_consumer = 1
    consumer = None
    topic = os.environ.get("kafka_topics")
    group = os.environ.get("kafka_consumer_group_name")

    if name_queue == "handler-information-for-report":
        from src.consumers.kafka.insert_history.insert_popup_history import (
            InsertInformationForReport,
        )
        consumer = InsertInformationForReport(topic_name=topic, group_id=group, client_mongo=base_client)

    if name_queue == "support-workflow-get-info":
        from src.consumers.kafka.insert_history.support_workflow_get_info import (
            SupportWorkflowGetInfo,
        )
        consumer = SupportWorkflowGetInfo(topic_name=topic, group_id=group, client_mongo=base_client)

    if consumer:
        consumer.start_consumer()
